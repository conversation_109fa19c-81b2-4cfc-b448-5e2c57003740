import asyncio
import time
import redis
from common.utils import singleton
from settings import *


# @singleton
class RedisWmb:
    def __init__(self):
        self.red: redis.Redis = redis.from_url(REDIS_URL, decode_responses=True)
        self.c = 0

    def save_cookies(self, key, value):
        logger.debug(f"key={key},val={value}")
        self.red.set(key, value, ex=EXPIRE)  # cookie键值对设置存活时间(12小时)

    def get_cookies_expire(self, key):
        return self.red.ttl(key)  # 获取键的存活时间

    async def async_get_cookies(self):
        return await asyncio.to_thread(self.get_cookies)  # 用于在一个单独的线程中运行一个阻塞的函数(self.get_cookies)，并等待其完成

    def get_cookies(self):
        # 最大重试次数3,self.red获取所有以 wmb_cookie_开头的键,以列表形式返回,self.c是下标,然后再获取对应key,self.c+1,然后返回该键的cookie最后返回出去,报错则sleep10s,3次都没有就记录错误日志
        for _ in range(3):
            # 获取所有以 wmb_cookie_开头的键,并列表返回
            cookies = self.get_cookies_key()
            try:
                key = cookies[self.c]
                self.c += 1
                self.c %= len(cookies)
                res = self.red.get(key)
                logger.debug({"cookies_key": key})
                return res
            except IndexError:
                time.sleep(10)
        logger.error("获取cookies失败！")

    def test_connect(self):
        # 尝试获取所有key值
        res = self.red.keys('*')
        print(res)

    def get_cookies_key(self):
        cookies = self.red.keys("wmb_cookie_*")
        return cookies if cookies else []

    def close(self):
        self.red.close()



async def test():
    p = RedisWmb()
    for _ in range(20):
        res = await p.async_get_cookies()
        print(res)


if __name__ == '__main__':
    asyncio.run(test())
