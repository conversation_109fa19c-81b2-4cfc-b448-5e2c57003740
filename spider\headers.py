from curd import RedisWmb
from .login import Account
from settings import logger


class Headers:

    def __init__(self):
        self.account = Account.account
        self.redis_wmb: RedisWmb = RedisWmb()

    def get_headers(self, use_cookies=True):
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0',
        }
        if use_cookies:
            cookies = self.redis_wmb.get_cookies() or ""
            logger.debug({"cookies": cookies})
            headers['Cookie'] = cookies
        return headers
