#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: countdown.py
Description: 倒计时
Author: Peng
Email: <EMAIL>
Date: 2023-04-17
"""

import time
from .color import Color


class CountDownGUI:
    # 窗口默认尺寸
    w, h = 600, 160
    # label字体默认尺寸
    font_size = 100
    font_size_cur = 100
    # label字体默认其他参数
    font_base = "Arial %s bold"
    # 鼠标坐标初始化
    mouseX = 0
    mouseY = 0
    # 字体颜色
    # font_color = 'Cyan'  # 青色
    # font_color = 'DarkCyan'  # 暗青色
    # font_color = 'Turquoise'  # 绿宝石
    font_color = 'Aqua'  # 浅绿色/水色
    # 背景颜色
    bg = 'black'
    state = 1

    def __init__(self, seconds, **kwargs):
        import tkinter as tk
        # 倒计时的秒数
        self.seconds = seconds
        # 初始化tkinter
        self.app = tk.Tk()
        # 设置标题
        self.app.title(kwargs.get('title', ''))
        # 结束语
        self.conclusion = kwargs.get('conclusion', '计时结束')

        # 初始窗口尺寸 + 相对偏移量
        self.app.geometry(f'{self.w}x{self.h}+50+50')

        # 切换全屏的bool值
        self.fullScreenState = False
        # 绑定鼠标点击事件
        self.app.bind("<Button-1>", self.mouse_down)
        # 绑定鼠标拖动事件
        self.app.bind("<B1-Motion>", self.mouse_move)
        # 绑定鼠标双击事件
        self.app.bind("<Double-Button-1>", self.switched_full_screen)
        # 绑定<Configure>事件，将其绑定到on_window_configure函数上
        self.app.bind("<Configure>", self.on_window_configure)
        # 绑定ESC按键
        self.app.bind("<Escape>", self.on_escape_key)
        # 初始化时间文本变量
        self.time_str = tk.StringVar()
        # 初始化文本标签
        self.text_label = tk.Label(
            self.app,
            textvariable=self.time_str,
            bg=self.bg,
            fg=self.font_color,
            font=self.font_base % self.font_size,
            width=100,
            height=100,
        )

    def run(self):
        # 绘制标签
        self.text_label.pack(anchor='center')
        # 动态刷新标签文本内容
        self.refresh()
        # 开启程序
        self.app.mainloop()

    def mouse_down(self, event):
        self.mouseX = event.x
        self.mouseY = event.y

    def mouse_move(self, event):
        # 根据鼠标位置更新窗口位置
        self.app.geometry(f'+{event.x_root - self.mouseX}+{event.y_root - self.mouseY}')

    def switched_full_screen(self, event):
        # 双击切换全屏
        self.fullScreenState = not self.fullScreenState
        self.app.attributes('-fullscreen', self.fullScreenState)

    def on_window_configure(self, event):
        window_w = event.width
        self.font_size_cur = int(window_w / self.w * self.font_size)
        self.text_label.config(font=self.font_base % self.font_size_cur)

    def on_escape_key(self, event):
        self.fullScreenState = False
        self.app.attributes('-fullscreen', self.fullScreenState)

    def refresh(self):
        # 刷新标签文本内容
        m, s = divmod(self.seconds, 60)
        h, m = divmod(m, 60)
        self.seconds -= 1
        if -3 <= self.seconds < 0:
            self.text_label.configure(font=self.font_base % int(self.font_size_cur * .7))
            self.time_str.set(self.conclusion)
        elif self.seconds < -3:
            self.state = 0
            self.app.destroy()
        else:
            self.time_str.set("%02d:%02d:%02d" % (h, m, s))
        self.text_label.after(1000, self.refresh)


class CountDownText:
    state = 1

    def __init__(self, seconds, title=''):
        self.title = title
        self.seconds = seconds

    def run(self):
        c1, c2 = Color(), Color()
        head, content, tail = '===============>', f" {self.seconds} ", '<==============='
        width = len(f"{head} {content} {tail}")
        if self.title:  # 输出固定标题
            title = self.title.encode().center(width).decode()
            print(c1.pure(title, color=31))
        while self.seconds >= 0:  # 动态替换颜色
            print(f"\r{c1.next(head)} {c1.pure(self.seconds, color=31)} {c2.last(tail)}", end='')
            self.seconds -= 1
            time.sleep(1)
        print()
        self.state = 0
