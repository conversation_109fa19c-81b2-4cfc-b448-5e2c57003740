import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
from spider.spider_queue import SpiderQueue
from spider.downloader import DownloaderWMB
from parser import ParserWmb
from curd import MySQLWmb
from settings import logger, WORKERS_COMPANY
from common.utils import statistics


class SpiderWmbCompany(SpiderQueue):
    def __init__(self):
        self.downloader: DownloaderWMB = DownloaderWMB()
        self.parser: ParserWmb = ParserWmb()
        self.db: MySQLWmb = MySQLWmb()

    async def run(self):
        await self.scheduler(WORKERS_COMPANY, 500)

    async def producer(self, queue: asyncio.Queue):
        aid = 0
        while True:
            logger.debug({"queue_size": queue.qsize()})
            data_src = await self.db.read_company_new(aid=aid, limit=500)
            if len(data_src) == 0:
                aid = 0
                await asyncio.sleep(60*30)  # 没有新数据时休眠30分钟
                continue
            for aid, company_id, company_type in data_src:
                await queue.put((company_id, company_type))

    async def task_one(self):
        """一次性任务 - 每天一次"""
        aid = 0
        while True:
            data_src = await self.db.read_company_new(aid=aid, limit=500)
            if len(data_src) == 0:
                logger.warning(f"没有新任务 or 任务完成")
                return
            for aid, company_id, company_type in data_src:
                await self.worker((company_id, company_type))

    async def worker(self, item):
        company_id, company_type = item
        logger.info({"company_id": company_id, "company_type": company_type})
        item = dict()
        item["company_id"] = company_id
        type_str = "buyer" if company_type == 0 else "supplier"
        item["source_url"] = f"/{type_str}/{company_id}"
        item_main = await self.company_main(company_id, company_type)
        item_contact = await self.company_contact(company_id)
        hs_code_str = await self.company_hs_code(company_id, company_type)
        product_str = await self.company_product(company_id)
        item.update(item_main)
        item.update(item_contact)
        item["hs_code"] = hs_code_str
        item["products"] = product_str
        # 保存公司信息
        await self.db.save_company(item)
        # 同步公司状态
        if not item.get('last_trade_date'):
            status = await self.db.update_company_status(company_id=company_id)
            logger.info(f"执行状态：{status}")
        else:
            last_trade_date = item['last_trade_date']
            await self.db.update_company_flag(company_id, company_type, last_trade_date)

    async def company_main(self, company_id: int, company_type: int):
        data = await self.downloader.search_company(company_id, company_type)
        if isinstance(data,int):
            return dict()
        return await self.parser.parse_company(data)

    async def company_contact(self, company_id: int):
        data = await self.downloader.search_contact(company_id)
        if isinstance(data, int):
            return dict()
        return await self.parser.parse_contacts(data)

    async def company_hs_code(self, company_id: int, company_type: int) -> str:
        data = await self.downloader.search_hs_code(company_id, company_type)
        if isinstance(data, int):
            return ''
        logger.debug(data)
        parsed_data = await self.parser.parse_hs_code(data)
        logger.debug({"parsed_data": parsed_data, "type": type(parsed_data)})
        if parsed_data is None or len(parsed_data) == 0:
            hs_code_str = ''
        else:
            for d in parsed_data:
                d["company_id"] = company_id
            await self.db.save_hs_code(parsed_data)
            hs_code_str = ','.join([r for d in parsed_data if (r := d.get("hs"))])
        return hs_code_str

    async def company_product(self, company_id: int) -> str:
        data = await self.downloader.search_product(company_id)
        if isinstance(data, int):
            return ''
        logger.debug(data)
        parsed_data = await self.parser.parse_product(data)
        logger.debug({"parsed_data": parsed_data, "type": type(parsed_data)})
        if parsed_data is None or len(parsed_data) == 0:
            product_str = ''
        else:
            for d in parsed_data:
                d["company_id"] = company_id
            await self.db.save_product(parsed_data)
            product_str = ','.join([r for d in parsed_data if (r := d.get("value"))])
        return product_str


async def test():
    p = SpiderWmbCompany()
    await p.worker((91009828, 1))


@statistics
def ignition():
    asyncio.run(test())


if __name__ == '__main__':
    ignition()
