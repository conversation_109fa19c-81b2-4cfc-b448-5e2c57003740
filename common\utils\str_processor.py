import time
from typing import Union


def timestamp2str(timestamp: Union[int, float]) -> str:
    """时间戳转时间字符串"""
    if isinstance(timestamp, int) and len(f"{timestamp}") == 13:
        timestamp /= 1000
    return time.strftime('%F %T', time.localtime(timestamp))  # %Y-%m-%d %H:%M:%S


def str2timestamp(time_str: str, ms=False) -> float:
    """时间字符串转时间戳"""
    struct_time = time.strptime(time_str, '%Y-%m-%d %H:%M:%S')
    f = time.mktime(struct_time)
    return int(f * 1000) if ms else int(f)


def now():
    return time.strftime('%F %T', time.localtime())  # %Y-%m-%d %H:%M:%S


def today():
    return time.strftime('%F', time.localtime())  # %Y-%m-%d %H:%M:%S
