import copy
import json
import re
from typing import Union, Tuple
from html import unescape
from parsel import Selector
from settings import logger
from spider.mode import ModeTradeSearch
from lxml import etree


class ParserWmb:
    def __init__(self):
        self.mapping_type = {"buyer": 0, "supplier": 1}
        self.regex_date = re.compile(r'[^\d-]')

    async def parse_countries(self, data: dict):
        country_set = set()
        states_list = data.get("data", dict()).get("list", list())
        for states in states_list:
            country_list = states.get("country_list", list())
            for c in country_list:
                country_cn_show = c.get('country_cn_show') or ""
                country_en = c.get('country_en') or ""
                if country_en != "":
                    country_set.add((country_en, country_cn_show))
        country_list = list(country_set)
        country_list.sort(key=lambda x: x[0])
        # return country_list
        if len(country_list) > 0:
            res_dict = {k[0]: {'en': k[0], 'cn': k[1], 'flag': 0} for k in country_list}
        else:
            res_dict = dict()
        # 添加国际公司，新增逻辑
        res_dict["*"] = {"en": "*", "cn": "国际公司", "flag": 0}
        return res_dict

    async def parse_task(self, data: dict) -> Union[Tuple[int, list], None]:
        total = data.get("total") or 0
        if total == 0:
            return
        logger.debug({"total": total})
        content = data.get("content") or ""
        if content == "":
            return

        # with open('./wmb_country.html', 'r', encoding='utf8') as f:
        # # with open(file, 'r', encoding='utf8') as f:
        #     content = f.read()

        res = list()

        content = unescape(content).replace(r'\"', "")
        html = Selector(content)
        data_list = html.xpath('//li')
        for data in data_list:
            # 公司名
            # name = data.xpath('./h2/a/text()').get('').strip()
            # 公司链接
            company_url = data.xpath('./h2/a/@href').get('')
            # 公司类型、编号
            _, company_type, company_id = company_url.split('/')
            info = data.xpath('./p[1]/text()').extract()
            # 国家
            country_info = info[0]
            country_info = country_info.split(' ')
            country = ' '.join(country_info[:-1]) if country_info[-1] in {"buyer", "supplier"} else ""
            # 交易量
            bill_count = info[-1].replace("笔交易", "")
            # 最后交易时间
            # last_trade_date = self.regex_date.sub('', data.xpath('./p[last()]/span/text()').get(''))
            last_trade_date = self.regex_date.sub('', data.xpath('.//span[@class="search-list-date"]/text()').get(''))
            # print('='*88)
            # print(name)
            # print(company_url)
            # print(company_type)
            # print(company_id)
            # print(country)
            # print(bill_count)
            # print(last_trade_time)
            item = dict()
            item["company_id"] = company_id
            item["company_type"] = self.mapping_type.get(company_type, company_type)
            item["country"] = "*" if country == 'other ' else country
            item["bill_count"] = bill_count
            item["last_trade_date"] = last_trade_date
            # print(item)
            res.append(item)
        return total, res

    async def parse_company(self, data: str) -> dict:
        html = Selector(data)
        info = html.xpath('//div[@class="details-head"]/@data-company-details').get('')
        logger.info(f"{info=}")
        if len(info) == 0 or not info.startswith("{"):
            return dict()
        try:  # 少量数据unescape处理后json无法读取
            info = json.loads(unescape(info))
        except json.decoder.JSONDecodeError:
            info = json.loads(info)
        url = html.xpath('//link[@rel="alternate"]/@href')
        model = 'supplier' if 'supplier' in url else 'buyer'
        name = info.get('name') or ''
        product = info.get('product')
        business = ','.join([p.get('value') for p in product]) if product else ''
        country = info.get('country_std_seller', '') if model == 'supplier' else info.get('country_std_buyer', '')
        address = info.get('address') or ''
        last_trade_date = info.get('last_trade_date') or info.get('year_trade_date') or ''
        item = dict()
        item["name"] = name
        item["business"] = business
        item["country"] = country or ''
        item["address"] = address
        item["last_trade_date"] = last_trade_date
        return item

    async def parse_contacts(self, data: dict) -> dict:
        info = data.get("data") or dict()
        manager = r if (r := info.get('contact')) else ''
        telephone = info.get('tel') or ''
        fax = info.get('fax') or ''
        email = info.get('email') or ''
        website = info.get('web') or ''
        item = dict()
        item["manager"] = manager
        item["telephone"] = telephone
        item["fax"] = fax
        item["email"] = email
        item["website"] = website
        return item

    async def parse_hs_code(self, data: dict):
        return data.get("hs", dict()).get("data", dict()).get("list", list())

    async def parse_product(self, data: dict):
        return data.get("product", dict()).get("data", dict()).get("list", list())

    async def parse_hscode_current_data(self,html_str):
        build_data_list = []
        html = etree.HTML(html_str)
        if html:
            items_xpath = './/table[@class="trade-data-list"]/tr'
            items_number = len(html.xpath(items_xpath))

            for item_index in range(items_number):
                build_id_xpath = f'{items_xpath}[{item_index+1}]/@data-billid'
                date_str_xpath = f'{items_xpath}[{item_index+1}]/td[1]//text()'
                date_str = html.xpath(date_str_xpath)
                build_id = html.xpath(build_id_xpath)
                if (date_str and date_str[0]) and (build_id and build_id[0]):
                    build_data_list.append(
                        {'id': build_id[0], 'trade_date': date_str[0]}
                    )
        return build_data_list

    async def parse_trade_detailed(self, data: dict):
        clean_data = {
            'company_id': '',
            'amount': data.get("amount") or '',
            'bill_no': data.get("bol_number") or '',
            'buyer': data.get("buyer") or '',
            'buyer_country': data.get("buyer_country") or '',
            'buyer_id': '',
            'buyer_id_std': '',
            'buyer_port': data.get("place_receipt") or '',
            'buyer_status': '',
            'container': '',
            'date': data.get("date") or '',
            'descript': data.get("descript") or '',
            'descript_label': '',
            'hs': data.get("hs") or '',
            'notify_name': data.get("notify_name") or '',
            'origin_countr': data.get("origin_country") or '',
            'qty': data.get("qty") or '',
            'qty_unit': data.get("quantity_unit") or '',
            'seller': data.get("seller") or '',
            'seller_country': data.get("seller_country") or '',
            'seller_id': '',
            'seller_id_std': '',
            'seller_port': '',
            'seller_status': '',
            'source': '',
            'trans': data.get("trans_type") or '',
            'uusd': data.get("declared_unit_price_in_fc") or '',
            'weight': data.get("weight") or data.get('n_weight_in_kg') or '',
            'weight_unit': data.get("weight_unit") or '',
        }
        return clean_data

    async def parse_trade(self, data: dict, item: ModeTradeSearch) -> tuple:
        # 贸易数据
        year_list = list()
        trade_list = list()
        company_id = item.company_id
        country = item.country
        company_type = item.company_type
        total = 0
        message = data.get("trade", dict()).get("message", '')
        if message != "Success":
            total = -1
        # year_base = {"company_id": company_id, "country": country, "company_type": company_type}
        year_base = {"company_id": company_id, "country": country, "company_type": company_type}
        # data = data[0]
        year_src = data.get("year", dict()).get('list', list())
        if len(year_src) > 0:
            for year_info in year_src:
                year = year_info.get("key_as_string", '')
                doc_count = year_info.get("doc_count", '')
                if year.isdigit() and isinstance(doc_count, int):
                    item_year = copy.deepcopy(year_base)
                    item_year["year"] = year
                    item_year["doc_count"] = doc_count
                    year_list.append(item_year)

        size = data.get("size", 0)
        if size == 0:
            logger.warning(f'size为0,company_id:{company_id}')
            return total, year_list, trade_list
        total = data.get("trade", dict()).get("data", dict()).get("hits") or 0
        trades = data.get("trade", dict()).get("data", dict()).get('list', list())
        for trade in trades:
            amount = trade.get("amount") or ''
            bill_no = trade.get("bill_no") or ''
            buyer = trade.get("buyer") or ''
            buyer_country = trade.get("buyer_country_ori") or ''
            buyer_id = trade.get("buyer_id") or 0
            buyer_id_std = trade.get("buyer_id_std") or 0
            buyer_port = trade.get("buyer_port") or ''
            buyer_status = trade.get("buyer_status") or ''
            container = trade.get("container") or ''
            date = trade.get("date") or ''
            date = date.replace('/', '-')
            descript = trade.get("descript") or ''
            descript = descript.replace('"', '\'')
            descript_label = trade.get("descript_label") or list()
            descript_label = ','.join([r for x in descript_label if (r := x.get('key'))])
            hs = trade.get("hs") or ''
            _id = trade.get("id") or 0
            if _id == 0:
                continue
            notify_name = trade.get("notify_name") or ''
            origin_country = trade.get("origin_country") or ''
            qty = trade.get("qty") or 0
            qty_unit = trade.get("qty_unit") or ''
            seller = trade.get("seller") or ''
            seller_country = trade.get("seller_country_ori") or ''
            seller_id = trade.get("seller_id") or 0
            seller_id_std = trade.get("seller_id_std") or 0
            seller_port = trade.get("seller_port") or ''
            seller_status = trade.get("seller_status") or ''
            source = trade.get("source") or ''
            trans = trade.get("trans") or ''
            uusd = trade.get("uusd") or 0
            weight = trade.get("weight") or ''
            weight_unit = trade.get("weight_unit") or ''
            item_trade = dict()
            item_trade['company_id'] = company_id
            item_trade['amount'] = amount
            item_trade['bill_no'] = bill_no
            item_trade['buyer'] = buyer
            item_trade['buyer_country'] = buyer_country
            item_trade['buyer_id'] = buyer_id
            item_trade['buyer_id_std'] = buyer_id_std
            item_trade['buyer_port'] = buyer_port
            item_trade['buyer_status'] = buyer_status
            item_trade['container'] = container
            item_trade['date'] = date
            item_trade['descript'] = descript
            item_trade['descript_label'] = descript_label
            item_trade['hs'] = hs
            item_trade['id'] = _id
            item_trade['notify_name'] = notify_name
            item_trade['origin_country'] = origin_country
            item_trade['qty'] = qty
            item_trade['qty_unit'] = qty_unit
            item_trade['seller'] = seller
            item_trade['seller_country'] = seller_country
            item_trade['seller_id'] = seller_id
            item_trade['seller_id_std'] = seller_id_std
            item_trade['seller_port'] = seller_port
            item_trade['seller_status'] = seller_status
            item_trade['source'] = source
            item_trade['trans'] = trans
            item_trade['uusd'] = uusd
            item_trade['weight'] = weight
            item_trade['weight_unit'] = weight_unit
            trade_list.append(item_trade)
        return total, year_list, trade_list
