import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import copy
from collections import deque
from datetime import datetime, timed<PERSON><PERSON>
from typing import Tuple
from spider.downloader import DownloaderWMB
from parser import ParserWmb
from curd import MySQLWmb
from settings import logger, WORKERS_TRADE
from spider.mode import ModeTradeSearch
from spider.spider_queue import SpiderQueue


class SpiderWmbTrade(SpiderQueue):
    def __init__(self, concurrency=WORKERS_TRADE):
        self.downloader: DownloaderWMB = DownloaderWMB()
        self.parser: ParserWmb = ParserWmb()
        self.db: MySQLWmb = MySQLWmb(concurrency=concurrency)

    async def worker_history(self, semaphore, item: ModeTradeSearch, year, status: bool = True):
        async with semaphore:
            await self.worker(item)
            if status:
                # 更新处理状态
                await self.db.update_status_to_history(item.company_id, item.company_type, year, status=1)
            # 同步统计信息
            await self.db.update_data_count_by_year_to_history(item.company_id, item.company_type, year)

    async def worker(self, item: ModeTradeSearch, update_data_count: bool = False):
        logger.info(','.join(map(str, item.__dict__.values())))
        # 搜索贸易数据
        start_time = item.start_time
        last_trade_date = item.last_trade_date
        item.start_time = start_time if start_time else '2019-01-01'
        item.last_trade_date = last_trade_date if last_trade_date else datetime.now().strftime('%Y-%m-%d')
        data = await self.downloader.search_trade(item)
        if isinstance(data, int):
            await self.db.update_last_trade_date_to_task_new(item.company_id, item.company_type, item.last_trade_date,0)
            return data
        # 解析贸易数据
        res = await self.parser.parse_trade(data, item)
        total, year_list, trade_list = res
        last_record_date = ''
        # 更新按年统计的的数据量（源网站提供）
        if len(year_list) > 0:
            await self.db.save_task_history(year_list, del_zero=False)
        # 保存第一页的贸易数据
        if len(trade_list) > 0:
            await self.db.save_trade(trade_list)
            last_record_date = max(trade['date'] for trade in trade_list)
            last_record_date = last_record_date.replace('/', '-')
        # 贸易数据翻页
        if total > 10000:
            # total = 10000
            # logger.warning("total > 10000")
            await self.worker_more_than_ten_thousand(item)
        elif 20 < total <= 10000:
            await self.worker_by_total(item, total, start=20)
        # 删除按年计数为0的情况（数据历史遗留问题，历史数据完成后计划报废）
        if total >= 0:
            await self.db.del_task_history_zero_doc(
                item.company_id, item.company_type, year=item.last_trade_date.split('-')[0])
        if last_record_date == '':
            return
        # 更新状态
        await self.db.update_last_trade_date_to_task_new(item.company_id, item.company_type, last_record_date, total)
        if not last_trade_date:
            await self.db.update_last_trade_date(company_id=item.company_id,company_type=item.company_type,last_trade_date=last_record_date)
        # 更新data_count
        if update_data_count and len(year_list) > 0:
            await self.db.update_data_count_to_history(item)
        logger.info(f'完成了家人们 company_id:{item.company_id}')

    async def worker_by_total(self, item: ModeTradeSearch, total: int, start: int = 0):
        for i in range(start, total, 20):
            item.start = i
            logger.info(','.join(map(str, item.__dict__.values())))
            data = await self.downloader.search_trade(item)
            if isinstance(data,int):
                continue
            res = await self.parser.parse_trade(data, item)
            _, year_list, trade_list = res
            if len(year_list) > 0:
                await self.db.save_task_history(year_list, del_zero=False)
            if len(trade_list) > 0:
                await self.db.save_trade(trade_list)

    async def worker_more_than_ten_thousand(self, item: ModeTradeSearch):
        """大于10000的需要分页爬取"""
        start = item.start_time
        end = item.last_trade_date
        part1, part2 = await self.second_part_of_the_date(start, end)
        queue = deque()
        queue.append(part1)
        queue.append(part2)
        while True:
            if len(queue) == 0:
                break
            start_cur, end_cur = queue.popleft()
            item_cur = copy.deepcopy(item)
            item_cur.start_time = start_cur
            item_cur.last_trade_date = end_cur
            total = await self.search_total_data(item_cur)
            if total == 0:
                continue
            elif total > 10000:
                if start_cur != end_cur:
                    part1, part2 = await self.second_part_of_the_date(start_cur, end_cur)
                    queue.append(part1)
                    queue.append(part2)
                    continue
                else:
                    info = {"country": item_cur.country, "date_start": start_cur, "date_end": end_cur, "total": total}
                    await self.db.save_task_country_error(info)
                    total = 10000
            await self.worker_by_total(item_cur, total)

    async def search_total_data(self, item: ModeTradeSearch) -> int:
        logger.info(','.join(map(str, item.__dict__.values())))
        # 搜索贸易数据
        data = await self.downloader.search_trade(item)
        if isinstance(data,int):
            return 0
        # 解析贸易数据
        res = await self.parser.parse_trade(data, item)
        total, year_list, trade_list = res
        return total

    @staticmethod
    async def second_part_of_the_date(start: str, end: str) -> Tuple[Tuple[str, str], Tuple[str, str]]:
        """
        二分日期
        Args:
            start: 时间字符串，例：2023-01-01
            end: 时间字符串，例：2023-12-31
        Returns:
            新的日期分片，例：(('2023-01-01', '2023-07-02'), ('2023-07-03', '2023-12-31'))
        """
        # 将字符串日期转换为日期对象
        start_date = datetime.strptime(start, "%Y-%m-%d")
        end_date = datetime.strptime(end, "%Y-%m-%d")
        # 计算中间日期
        half_diff = (end_date - start_date) // 2
        middle_date = start_date + half_diff
        return (start, middle_date.strftime("%Y-%m-%d")), ((middle_date + timedelta(days=1)).strftime("%Y-%m-%d"), end)

    async def read_task_new(self, aid: int = 0, limit: int = 500):
        return await self.db.read_task_trade_new(aid=aid, limit=limit)

    async def read_task_history(self, year: int, limit: int = 500):
        return await self.db.read_task_trade_history(year, limit)

    async def read_task_history_by_status(self, year: int, aid: int = 0, limit: int = 500, is_rg=False):
        if not is_rg:
            return await self.db.read_task_trade_history_by_status(year, aid, limit)
        else:
            # 人工状态 不管状态是否已完成，都进行重跑
            return await self.db.read_task_trade_history_by_status_rg(year, aid, limit)

    async def close(self):
        await self.db.close()


"""
docker compose logs -f 2>&1 | grep "35222267"
"""

if __name__ == '__main__':
    words = SpiderWmbTrade()
    company_list = [62740804]
    for company_id in company_list:
        word_item = ModeTradeSearch(
            company_id= company_id,
            company_type=0,
            start_time = '2025-01-01',
            last_trade_date= '2025-07-20',
        )
        asyncio.run(words.worker(item=word_item))
