#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: wmb_manual_supplement.py
Description: 手动补录任务脚本 - 处理今年1月到7月的数据
Author: AI Assistant
Date: 2025-01-08
"""

import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

import asyncio
from spider import SpiderCompanyToTrade
from common.utils import statistics


async def manual_supplement():
    """
    执行手动补录任务
    处理时间范围：今年1月到7月
    处理所有国家和公司类型的组合
    """
    spider = SpiderCompanyToTrade()
    await spider.run()


@statistics
def ignition():
    """
    启动手动补录任务
    """
    asyncio.run(manual_supplement())


if __name__ == '__main__':
    print("开始执行手动补录任务...")
    print("时间范围：今年1月到7月（2025-01 到 2025-07）")
    print("处理所有国家和公司类型的组合")
    print("=" * 50)
    ignition()
