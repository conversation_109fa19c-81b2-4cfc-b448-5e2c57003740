# 人工启动；仅用于数据临时补充抓取;跑临时任务；一次性任务；等

version: '3.8'  # docker-compose.yml文件格式的版本号
services:
  spider_wmb_scheduler: # 容器名(前面会自动加上项目目录的名字)
    container_name: spider_wmb_scheduler_rg  # 容器名(指定完整的容器名，会忽略上面的容器名)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: spider_wmb_rg:0.1  # 镜像名:标签
    command: python bin/wmb_scheduler.py  # 容器的启动命令
    volumes: # 数据卷
      - ./logs:/app/logs
    logging: # 日志
      options:
        max-size: "10m"
        max-file: "3"
    restart: on-failure  # 非0 状态码结束时重启


  spider_wmb_trade_2023: # 容器名(前面会自动加上项目目录的名字)
    container_name: spider_wmb_trade_2023_rg  # 容器名(指定完整的容器名，会忽略上面的容器名)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: spider_wmb_rg:0.1  # 镜像名:标签
    command: python bin/wmb_trade_2023.py  # 容器的启动命令
    volumes: # 数据卷
      - ./logs:/app/logs
    logging: # 日志
      options:
        max-size: "10m"
        max-file: "3"
    restart: on-failure  # 非0 状态码结束时重启
    depends_on:
      - spider_wmb_scheduler


#  spider_wmb_trade_his: # 容器名(前面会自动加上项目目录的名字)
#    container_name: spider_wmb_trade_his_rg  # 容器名(指定完整的容器名，会忽略上面的容器名)
#    build:
#      context: .  # 指定当前目录为Dockerfile的目录
#      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
#    image: spider_wmb_rg:0.1  # 镜像名:标签
#    command: python bin/wmb_trade_his.py  # 容器的启动命令
#    volumes: # 数据卷
#      - ./logs:/app/logs
#    logging: # 日志
#      options:
#        max-size: "10m"
#        max-file: "3"
#    restart: on-failure  # 非0 状态码结束时重启
#    depends_on:
#      - spider_wmb_scheduler
#
#  spider_wmb_trade_his1: # 容器名(前面会自动加上项目目录的名字)
#    container_name: spider_wmb_trade_his1_rg # 容器名(指定完整的容器名，会忽略上面的容器名)
#    build:
#      context: .  # 指定当前目录为Dockerfile的目录
#      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
#    image: spider_wmb_rg:0.1  # 镜像名:标签
#    command: python bin/wmb_trade_his1.py  # 容器的启动命令
#    volumes: # 数据卷
#      - ./logs:/app/logs
#    logging: # 日志
#      options:
#        max-size: "10m"
#        max-file: "3"
#    restart: on-failure  # 非0 状态码结束时重启
#    depends_on:
#      - spider_wmb_scheduler

  spider_wmb_trade_2024: # 容器名(前面会自动加上项目目录的名字)
    container_name: spider_wmb_trade_2024_rg  # 容器名(指定完整的容器名，会忽略上面的容器名)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: spider_wmb_rg:0.1  # 镜像名:标签
    command: python bin/wmb_trade_2024.py  # 容器的启动命令
    volumes: # 数据卷
      - ./logs:/app/logs
    logging: # 日志
      options:
        max-size: "10m"
        max-file: "3"
    restart: on-failure  # 非0 状态码结束时重启
    depends_on:
      - spider_wmb_scheduler

  # spider_wmb_trade_supplement: # 数据补录
  #   container_name: wmb_supplement_data_company  # 容器名(指定完整的容器名，会忽略上面的容器名)
  #   build:
  #     context: .  # 指定当前目录为Dockerfile的目录
  #     dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
  #   image: spider_wmb_rg:0.1  # 镜像名:标签
  #   command: python bin/wmb_supplement_data_company.py  # 容器的启动命令
  #   volumes: # 数据卷
  #     - ./logs:/app/logs
  #   logging: # 日志
  #     options:
  #       max-size: "10m"
  #       max-file: "3"

  spider_wmb_trade_2025: # 容器名(前面会自动加上项目目录的名字)
    container_name: spider_wmb_trade_2025_rg  # 容器名(指定完整的容器名，会忽略上面的容器名)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: spider_wmb_rg:0.1  # 镜像名:标签
    command: python bin/wmb_trade_2025.py  # 容器的启动命令
    volumes: # 数据卷
      - ./logs:/app/logs
    logging: # 日志
      options:
        max-size: "10m"
        max-file: "3"
    restart: on-failure  # 非0 状态码结束时重启
    depends_on:
      - spider_wmb_scheduler