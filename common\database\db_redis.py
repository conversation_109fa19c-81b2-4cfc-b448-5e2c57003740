#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: db_redis.py
Description: redis常用封装
Author: Peng
Email: <EMAIL>
Date: 2023-07-07
"""

import math
import redis
import json


class PriorityQueue:
    def __init__(self, name, redis_conn):
        """
        优先队列：有序集合，去重：集合
        :param name: 队列的前缀
        :param redis_conn: redis连接实例
        """
        self.queue = name + ":queue"
        self.dedupe = name + ":dedupe"
        self.redis = redis_conn

    def push(self, *infos):
        pipe = self.redis.pipeline()
        for info in infos:
            if isinstance(info, dict):
                key = info['key']
                score = info.get('score', 1)
                info = json.dumps(info, ensure_ascii=False)
            else:
                raise TypeError(f"Expected type <class 'dict'>, but got {type(info)}.")
            if not self.redis.sismember(self.dedupe, key):
                pipe.zadd(self.queue, {info: score})
                pipe.sadd(self.dedupe, key)
        pipe.execute()

    def pop(self, count=1, desc=False):
        elements = self.redis.zrange(self.queue, 0, count - 1, desc=desc)
        pipe = self.redis.pipeline()
        for element in elements:
            pipe.zrem(self.queue, element)
        pipe.execute()
        return elements

    def _pop(self, count=1, desc=False):
        script = """
            local elements = redis.call('ZRANGE', KEYS[1], 0, ARGV[1] - 1, ARGV[2])
            if next(elements) ~= nil then
                redis.call('ZREM', KEYS[1], unpack(elements))
            end
            return elements
        """
        return self.redis.eval(script, 1, self.queue, count, desc)

    def __len__(self):
        return self.redis.zcard(self.queue)

    def size(self):
        return self.__len__()

    def is_empty(self):
        return self.__len__() == 0


class AsyncPriorityQueue:
    def __init__(self, name, redis_conn):
        """
        优先队列：有序集合，去重：集合，当不清楚任务上限时用这个
        :param name: 队列的前缀
        :param redis_conn: redis连接实例
        """
        self.queue = name + ":queue"
        self.dedupe = name + ":dedupe"
        self.redis = redis_conn

    async def push(self, *infos):
        pipe = await self.redis.pipeline()
        for info in infos:
            if isinstance(info, dict):
                key = info['key']
                score = info.get('score', 1)
                info = json.dumps(info, ensure_ascii=False)
            else:
                raise TypeError(f"Expected type <class 'dict'>, but got {type(info)}.")
            if not await self.redis.sismember(self.dedupe, key):
                await pipe.zadd(self.queue, {info: score})
                await pipe.sadd(self.dedupe, key)
        await pipe.execute()

    async def repush(self, element):
        pass

    async def pull(self, count=1, desc=False):
        elements = await self.redis.zrange(self.queue, 0, count - 1, desc=desc)
        pipe = await self.redis.pipeline()
        for element in elements:
            await pipe.zrem(self.queue, element)
        await pipe.execute()
        return elements

    async def size(self):
        # return {"task": self.redis.zcard(self.task), "dedupe": self.redis.scard(self.dedupe)}
        return await self.redis.zcard(self.queue)

    async def is_empty(self):
        # return self.size()["task"] == 0
        return await self.size() == 0

    async def get_priority(self, element):
        return await self.redis.zscore(self.queue, element)


class SetQueue:
    def __init__(self, redis_conn, name):
        """
        集合队列
        :param name: 队列的前缀
        :param redis_conn: redis连接实例
        """
        self.queue = name + ":queue"
        self.dedupe = name + ":dedupe"
        self.redis = redis_conn

    def push(self, *infos):
        pipe = self.redis.pipeline()
        for info in infos:
            if isinstance(info, dict):
                key = info['key']
                info = json.dumps(info, ensure_ascii=False)
            else:
                raise TypeError(f"Expected type <class 'dict'>, but got {type(info)}.")
            if not self.redis.sismember(self.dedupe, key):
                pipe.sadd(self.queue, info)
                pipe.sadd(self.dedupe, key)
        pipe.execute()

    def repush(self, *info):
        info = json.dumps(info, ensure_ascii=False)
        self.redis.sadd(self.queue, *info)

    def pull(self, count=1):
        return self.redis.spop(self.queue, count)

    def size(self):
        return self.redis.scard(self.queue)

    def is_empty(self):
        return self.size() == 0


class AsyncSetQueue:
    def __init__(self, redis_conn, name):
        """
        集合队列
        :param name: 队列的前缀
        :param redis_conn: redis连接实例
        """
        self.queue = name + ":queue"
        self.dedupe = name + ":dedupe"
        self.redis = redis_conn

    async def push(self, *infos):
        pipe = await self.redis.pipeline()
        for info in infos:
            if isinstance(info, dict):
                key = info['key']
                info = json.dumps(info, ensure_ascii=False)
            else:
                raise TypeError(f"Expected type <class 'dict'>, but got {type(info)}.")
            if not await self.redis.sismember(self.dedupe, key):
                await pipe.sadd(self.queue, info)
                await pipe.sadd(self.dedupe, key)
        await pipe.execute()

    async def repush(self, *info):
        info = json.dumps(info, ensure_ascii=False)
        await self.redis.sadd(self.queue, *info)

    async def pull(self):
        return await self.redis.spop(self.queue)

    async def pulls(self, count=1):
        return await self.redis.spop(self.queue, count)

    async def size(self):
        return await self.redis.scard(self.queue)

    async def is_empty(self):
        return await self.size() == 0


class Dequeue:
    def __init__(self, redis_conn, name):
        """
        双端队列：列表，去重：集合
        :param name: 队列的前缀
        :param redis_conn: redis连接实例
        """
        self.task = name + ":queue"
        self.dedupe = name + ":dedupe"
        self.redis = redis_conn

    def lpush(self, *elements):
        pass

    def rpush(self, *elements):
        pass

    def lpull(self, count=1):
        pass

    def rpull(self, count=1):
        pass

    def size(self):
        return self.redis.llen(self.task)

    def is_empty(self):
        return self.size() == 0


class BloomFilter:
    def __init__(self, red: redis, key, n=1000000, p=0.01):
        if n > 448089842:
            raise KeyError("The maximum value of n is 448089842.")
        self.n = n
        self.p = p
        bit_size, hash_count = self.optimal_params
        self.key = key
        self.bit_size = bit_size
        self.hash_count = hash_count
        self.red = red
        # load the lua scripts from files
        with open("./add.lua") as f:
            self.add_script = f.read()
        with open("./contains.lua") as f:
            self.contains_script = f.read()

    @property
    def optimal_params(self):
        bit_size = -self.n * math.log(self.p) / (math.log(2) ** 2)
        hash_count = bit_size / self.n * math.log(2)
        return int(bit_size), int(hash_count)

    def add(self, value):
        # execute the add script with the key and arguments
        self.red.eval(self.add_script, 1, self.key, self.bit_size, self.hash_count, value)

    def __contains__(self, value):
        # execute the contains script with the key and arguments
        return bool(self.red.eval(self.contains_script, 1, self.key, self.bit_size, self.hash_count, value))


def test_bf():
    red = redis.Redis.from_url("redis://:test@127.0.0.1:6397/0", charset='utf8')
    bl_filter = BloomFilter(red, 'test')
    bl_filter.add('test1')

    res = 'test1' in bl_filter
    print(res)


def test(n, p):
    bit_size = -n * math.log(p) / (math.log(2) ** 2)
    hash_count = bit_size / n * math.log(2)
    print("当前size", int(bit_size), int(hash_count))
    print("上限size", 2 ** 32)


if __name__ == '__main__':
    # test(448089842, .01)
    test_bf()
