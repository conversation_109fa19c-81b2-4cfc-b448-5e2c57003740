# import asyncio
# import copy
#
# from common.database import PoolMysql, AsyncDBPoolMysql
# from common.utils import statistics
# from settings import *
#
#
# class DBCustoms:
#     def __init__(self):
#         pool_customs = PoolMysql(**MYSQL_CUSTOMS)
#         self.db_customs = AsyncDBPoolMysql(pool_customs)
#
#         pool_target = PoolMysql(**MYSQL_TARGET)
#         self.db_target = AsyncDBPoolMysql(pool_target)
#
#         self.table = "t_trade_count_by_year_wmb"
#         self.table_last_date = "t_trade_count_last_date_wmb"
#
#     async def test_connect(self):
#         sql = f"show tables;"
#         res = await self.db_customs.read(sql)
#         print(res)
#         print('=' * 88)
#         res = await self.db_target.read(sql)
#         print(res)
#
#     async def reset_flag(self):
#         min_id, max_id = 1, 15990856
#         batch_size = 1000
#         for start, end in self.db_customs.id_iterator(min_id, max_id, batch_size):
#             logger.info({"start": start, "end": end, "max_id": max_id})
#             sql = (f"update wmb_task_new t1 inner join `cache` t2 on t1.company_id=t2.openid "
#                    f"set t1.flag=1 where t2.aid between  {start} and {end}")
#             await self.db_target.execute(sql)
#
#     async def load_exists_company_id(self):
#         min_id, max_id = 1, 68287809
#         batch_size = 10000
#         for start, end in self.db_customs.id_iterator(min_id, max_id, batch_size):
#             logger.info({"start": start, "end": end, "max_id": max_id})
#             # read
#             sql = f"select openid from t_company where company_id between {start} and {end} and source=1"
#             # print(sql)
#             data_src = await self.db_customs.read(sql)
#             if data_src is None or len(data_src) == 0:
#                 continue
#             # print(data_src)
#             # write
#             # sql = f"update wmb_task_new set flag=1 where company_id=%s"
#             sql = f"insert ignore into cache(openid) value(%s)"
#             update_list = [[int(d[0])] for d in data_src]
#             # print(update_list)
#             await self.db_target.write(sql, update_list)
#
#     async def load_last_trade_date(self):
#         min_id, max_id = await self.db_customs.read_id_range('t_company', 'company_id', 'source=1')
#         min_id = 5157701
#         logger.info({"min_id": min_id, "max_id": max_id})
#         for start, end in self.db_customs.id_iterator(min_id, max_id, batch_size=100):
#             await self.load_last_trade_date_worker((start, end, max_id, 1))
#             await self.load_last_trade_date_worker((start, end, max_id, 0))
#             # break
#
#     async def load_last_trade_date_worker(self, item):
#         logger.info(item)
#         start, end, max_id, company_type = item
#         if company_type == 1:
#             openid_name = "t2.seller_openid"
#         elif company_type == 0:
#             openid_name = "t2.buyer_openid"
#         else:
#             logger.error(f"company_type using case seller|buyer, got {company_type}")
#             return
#         # read
#         sql = (f"select t1.openid openid, {company_type} company_type, t1.country, "
#                f"date_format(from_unixtime(max(t2.trade_date)/1000), '%Y-%m-%d') last_record_date "
#                f"from t_company t1 inner join t_trade t2 on t1.openid={openid_name} "
#                f"where t1.company_id between {start} and {end} and t1.source=1 and t2.source=1 "
#                f"group by openid")
#         data_src = await self.db_customs.read(sql, return_dict=True)
#         if data_src is None or len(data_src) == 0 or data_src[0] is None:
#             return
#         # save
#         # print(data_src)
#         await self.db_target.save(table=self.table_last_date, items=data_src)
#
#     async def load_history_1(self):
#         min_id, max_id = 1, 20972158
#         batch_size = 10000
#         for start, end in self.db_customs.id_iterator(min_id, max_id, batch_size):
#             logger.info({"start": start, "end": end})
#             # read
#             sql = (f"select t1.openid, t2.country, t1.`year`, t1.buyer_count, t1.seller_count "
#                    f"from t_trade_count_by_year_wmb t1 inner join t_company t2 on t1.openid=t2.openid "
#                    f"where t1.aid between {start} and {end} and t2.source=1")
#             data_src = await self.db_customs.read(sql)
#             if data_src is None or len(data_src) == 0 or data_src[0][0] is None:
#                 continue
#             # print(data_src)
#             insert_list = list()
#             for data in data_src:
#                 openid, country, year, buyer_count, seller_count = data
#                 item = dict()
#                 item['openid'] = openid
#                 item['country'] = country
#                 item['year'] = year
#                 if buyer_count > 0:
#                     item_buyer = copy.deepcopy(item)
#                     item_buyer['company_type'] = 0
#                     item_buyer['data_count'] = buyer_count
#                     insert_list.append(item_buyer)
#                 if seller_count > 0:
#                     item_seller = copy.deepcopy(item)
#                     item_seller['company_type'] = 1
#                     item_seller['data_count'] = seller_count
#                     insert_list.append(item_seller)
#             # print(insert_list)
#             await self.db_target.save(self.table, insert_list)
#             # break
#
#     async def load_history(self):
#         min_id, max_id = await self.db_customs.read_id_range('t_company', 'company_id', 'source=1')
#         logger.info({"min_id": min_id, "max_id": max_id})
#         for start, end in self.db_customs.id_iterator(min_id, max_id, batch_size=100):
#             await self.load_history_worker((start, end, max_id, 'seller'))
#             await self.load_history_worker((start, end, max_id, 'buyer'))
#
#     async def load_history_worker(self, item):
#         logger.info(item)
#         start, end, max_id, company_type = item
#         if company_type == 'seller':
#             openid_name = "t2.seller_openid"
#             count_name = "seller_count"
#         elif company_type == 'buyer':
#             openid_name = "t2.buyer_openid"
#             count_name = "buyer_count"
#         else:
#             logger.error(f"company_type using case seller|buyer, got {company_type}")
#             return
#         # read
#         sql = (f"select {openid_name} openid, year(FROM_UNIXTIME(t2.trade_date/1000)) `year`, count(*) {count_name} "
#                f"from t_company t1 inner join t_trade t2 on t1.openid={openid_name} "
#                f"where t1.company_id between {start} and {end} and t1.source=1 and t2.source=1 "
#                f"group by openid, `year`")
#         data_src = await self.db_customs.read(sql, return_dict=True)
#         if data_src is None or len(data_src) == 0 or data_src[0] is None:
#             return
#         # save
#         await self.db_customs.save(table=self.table, items=data_src)
#
#
# async def test():
#     p = DBCustoms()
#     # p.db.test_connect()
#     # await p.load_history()
#     # await p.load_last_trade_date()
#     # await p.load_exists_company_id()
#     await p.reset_flag()
#     # await p.load_history_2()
#     # await p.test_connect()
#
#
# @statistics
# def ignition():
#     asyncio.run(test())
#
#
# if __name__ == '__main__':
#     ignition()
