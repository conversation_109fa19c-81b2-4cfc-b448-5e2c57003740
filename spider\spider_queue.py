import sys
import asyncio
import traceback

from settings import logger


class SpiderQueue:

    # async def run(self):
    #     pass

    async def scheduler(self, workers: int, max_size: int, producer=None):
        """通用内存队列调度器"""
        queue = asyncio.Queue(maxsize=max_size)
        if producer is None:
            producer = self.producer
        prod = asyncio.create_task(producer(queue))
        consumers = [asyncio.create_task(self.consumer(queue)) for _ in range(workers)]
        await prod
        await queue.join()
        await asyncio.wait(consumers)

    async def producer(self, queue: asyncio.Queue):
        """具体生产者"""
        pass

    async def consumer(self, queue: asyncio.Queue):
        """通用队列消费者"""
        while True:
            try:
                item = await queue.get()
                await self.worker(item)
            except Exception as e:
                # exc_type, exc_obj, exc_tb = sys.exc_info()
                # logger.warning(f"{exc_type.__name__}: {e}")
                traceback.print_exc()
                await asyncio.sleep(1)
            finally:
                queue.task_done()

    async def worker(self, item):
        """具体执行器"""
        pass
