import json
import time
import asyncio
import requests
import random
from typing import Union
from settings import logger, TIMEOUT
from spider.mode import ModeTradeSearch, ModeSearchTask
from .headers import Headers
from .proxy import ProxyTunnel


class DownloaderWMB:
    def __init__(self):
        self.header = Headers()  # 获取请求头对象(支持插入cookie,use_cookies=True则插入,默认插入)
        self.proxy = ProxyTunnel()  # 获取代理

    async def search_countries(self, company_type: int = 0):
        """访问国家列表"""
        company_type_str = "buyer" if company_type == 0 else "supplier"
        params = {
            "start": "0",
            # "key": "",
            # "hs": "*",
            "company_type": f"{company_type}",
            # "off_line_counties": "*",
            "search_type": "2",
            # "search_language": "0",
            # "search_relation": "0",
            "filter_bill_count_min": "0",
            "filter_bill_count_max": "0",
            # "filter_weight": "default",
            # "trade_countries": "*",
            # "buyer_ports": "*",
            # "seller_ports": "*",
            # "company_mark": "*",
            # "filter_date_start": "*",
            # "filter_date_end": "*"
        }
        url = "https://www.52wmb.com/async/common/country/stats"
        url = self.encode_params(url, params)
        headers = self.header.get_headers(use_cookies=True)
        referer = f"https://www.52wmb.com/{company_type_str}"
        headers["Referer"] = referer
        proxies = self.proxy.get_proxy()
        response = await self.request(url, headers, proxies)
        if isinstance(response, int):
            return response
        return dict() if response is None else response.json()

    async def search_task(self, item: ModeSearchTask) -> Union[dict, int]:
        """访问公司列表页"""
        # 生成链接
        params = {
            "start": f"{item.start}",
            "key": f"{item.key}",
            "hs": f"{item.hs}",
            "company_type": f"{item.company_type}",  # 0:buyer;1:supplier
            # "off_line_counties": "*",
            "search_type": f"{item.search_type}",
            # "search_language": "0",
            # "search_relation": "0",
            "sort": "last_trade_date",  # 最后交易时间排序
            "country": f"{item.country}",
            # "is_add_log": "false",
            # "filter_bill_count_min": "0",
            # "filter_bill_count_max": "0",
            # "filter_weight": "default",
            # "trade_countries": "*",
            # "buyer_ports": "*",
            # "seller_ports": "*",
            # "company_mark": "*",
            "filter_date_start": item.date_start,
            "filter_date_end": item.date_end,
        }
        url = "https://www.52wmb.com/async/company/search"
        url = self.encode_params(url, params)
        # print(url)
        # 获取参数
        headers = self.header.get_headers(use_cookies=item.use_cookies)
        proxies = self.proxy.get_proxy()
        # 请求数据
        response = await self.request(url, headers, proxies)
        if isinstance(response, int):
            return response
        return dict() if response is None else response.json()

    async def search_company(self, company_id: int, company_type: int) -> int | str:
        """访问公司详情页 - 主页"""
        type_str = "buyer" if company_type == 0 else "supplier"
        url = f"https://www.52wmb.com/{type_str}/{company_id}"
        # 获取参数
        headers = self.header.get_headers()
        proxies = self.proxy.get_proxy()
        # 请求数据
        response = await self.request(url, headers, proxies)
        if isinstance(response, int):
            return response
        return '' if response is None else response.text

    async def search_contact(self, company_id: int):
        """访问联系方式api"""
        url = f'https://www.52wmb.com/async/company/contacts?id={company_id}&type=0'
        # 获取参数
        headers = self.header.get_headers()
        proxies = self.proxy.get_proxy()
        # 请求数据
        response = await self.request(url, headers, proxies)
        if isinstance(response, int):
            return response
        return dict() if response is None else response.json()

    async def search_hs_code(self, company_id: int, company_type: int):
        """访问hs code api"""
        params = {
            "id": f"{company_id}",
            "company_type": f"{company_type}",
            "country": "*",
            # "key": "*",
            # "product": "*",
            "sort": "default",
            # "start_time": "2021-10-11",
            # "seo_flag": "0",
            # "is_page": "false",
            "start": "0",
            "size": "3000",
            # "tag_id": "0",
            # "reftoken": "",
            # "scene": "2"
        }
        url = "https://www.52wmb.com/company/detail/hs"
        url = self.encode_params(url, params)
        # 获取参数
        headers = self.header.get_headers()
        proxies = self.proxy.get_proxy()
        # 请求数据
        response = await self.request(url, headers, proxies)
        if isinstance(response, int):
            return response
        return dict() if response is None else response.json()

    async def search_product(self, company_id: int):
        """访问产品列表api"""
        params = {
            "id": f"{company_id}",
            # "key": "*",
            # "product": "*",
            "sort": "default",
            # "start_time": "**",
            # "seo_flag": "0",
            # "is_page": "true",
            "start": "0",
            "size": "3000",
            # "tag_id": "0",
            # "reftoken": "",
            # "top_count": "0",
            # "scene": "2"
        }
        url = "https://www.52wmb.com/async/detail/product-list"
        url = self.encode_params(url, params)
        # 获取参数
        headers = self.header.get_headers()
        proxies = self.proxy.get_proxy()
        # 请求数据
        response = await self.request(url, headers, proxies)
        if isinstance(response, int):
            return response
        return dict() if response is None else response.json()

    async def search_hsode_data(self, country, start_date, end_date, start=0, **kwargs):
        """
        获取关于国家的海关数据概况
        """

        ie = kwargs.get('ie', 0)  # 进口数据（0是进口,1是出口）
        hs = kwargs.get('hs', '')  # 海关编码
        des = kwargs.get('des', '')  # 产品描述
        seller = kwargs.get('seller', '')  # 供应商
        buyer = kwargs.get('buyer', '')  # 采购商
        buyer_country = kwargs.get('buyer_country', '')  # 采购国
        seller_country = kwargs.get('seller_country', '')  # 供应国
        seller_port = kwargs.get('seller_port', '')  # 出口港
        weight_min = kwargs.get('weight_min', '')  # 最小重量
        weight_max = kwargs.get('weight_max', '')  # 最大重量
        qty_min = kwargs.get('qty_min', '')  # 最小数量
        qty_max = kwargs.get('qty_max', '')  # 最大数量
        tag_id = kwargs.get('tag_id', '')  # 标签id(不必理会)
        url = 'https://www.52wmb.com/async/raw/trade/list'
        params = {
            'country': country,
            'ie': ie,
            'start_date': start_date,
            'end_date': end_date,
            'hs': hs,
            'des': des,
            'seller': seller,
            'buyer': buyer,
            'buyer_country': buyer_country,
            'seller_country': seller_country,
            'seller_port': seller_port,
            'weight_min': weight_min,
            'weight_max': weight_max,
            'qty_min': qty_min,
            'qty_max': qty_max,
            'tag_id': tag_id,
            'start': start
        }
        print('start',start)
        url = self.encode_params(url, params)
        headers = self.header.get_headers()
        proxies = self.proxy.get_proxy()
        # 请求数据
        response = await self.request(url, headers, proxies)
        return dict() if response is None else response.json()

    async def search_customs_detailed(self, trade_data):
        """
        获取关于国家的海关数据概况后,获取每一条数据的id,再去获取详情
        """
        build_id = trade_data.get('id', '')
        ie = trade_data.get('ie', 0)  # 进口数据（0是进口,1是出口）
        trade_date = trade_data.get('trade_date', '')  # 进口数据（0是进口,1是出口）
        country = trade_data.get('country', '')  # 进口数据（0是进口,1是出口）
        params = {
            'id': build_id,
            'ie': ie,
            'trade_date': trade_date,
            'country': country
        }
        url = "https://www.52wmb.com/async/raw/bill/detail"
        url = self.encode_params(url, params)
        # 获取参数
        headers = self.header.get_headers()
        proxies = self.proxy.get_proxy()
        # 请求数据
        reset = 3
        while reset:
            response = await self.request(url, headers, proxies)
            if isinstance(response, int):
                return dict()
            if response:
                if response.status_code == 200:
                    result = json.loads(response.content.decode())
                    return result

        return dict()

    async def search_trade(self, item: ModeTradeSearch):
        """访问贸易数据页"""
        params = {
            "id": f"{item.company_id}",
            "company_type": f"{item.company_type}",  # 0:buyer;1:supplier
            "end_time": item.last_trade_date,
            "last_trade_date": item.last_trade_date,
            "country": item.country or "*",
            # "key": "",
            # "product": "*",
            "sort": "last_trade_date",  # last_trade_date
            "start_time": item.start_time,
            # "seo_flag": "0",
            # "is_page": "true",
            "start": f"{item.start}",
            "size": "20",
            # "tag_id": "0",
            # "reftoken": "",
            "hs": f"{item.hs}",
            # "trade_country": "",
            # "trade_port": "",
            # "bill_no": "",
            # "source_country": "",
            "scene": "4"
        }
        url = "https://www.52wmb.com/company/detail/trade"
        url = self.encode_params(url, params)
        # 获取参数
        headers = self.header.get_headers()
        proxies = self.proxy.get_proxy()
        # 请求数据
        response = await self.request(url, headers, proxies)
        if isinstance(response, int):
            return response
        return dict() if response is None else response.json()

    @staticmethod
    def encode_params(url: str, params: dict):
        """拼接链接"""
        return f"{url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}"

    async def request(self, url, headers, proxies):
        logger.debug({"url": url})
        return await asyncio.to_thread(self._request, url, headers, proxies)

    @staticmethod
    def _request(url, headers, proxies):
        # 尝试请求三次,3次内成功并且状态码是200就返回response对象,如果是404就返回404,如果是其他则走异常处理路线,如果异常中是429就记录错误日志,并sleep随机时间并且记录警告日志,如果是其他,则直接记录警告日志
        for _ in range(3):
            try:
                resp = requests.get(url, headers=headers, proxies=proxies, timeout=TIMEOUT)
                info = f"{resp.status_code},{url}"
                if resp.status_code == 404:
                    logger.warning(info)
                    return 404
                # logger.info(info)
                resp.raise_for_status()
                return resp
            except Exception as e:
                if '429 Too Many Requests' in str(e):
                    info = f"429,{url}"
                    logger.error(info)
                    time.sleep(random.random())
                    continue
                logger.warning({"e": e, "url": url})
        logger.error({"e": "重试后失败请求失败", "url": url})


if  __name__ == '__main__':
    # print(asyncio.run(DownloaderWMB().search_countries(0)))
    print(asyncio.run(DownloaderWMB().search_hsode_data(country='bangladesh', start_date='2023-05-31', end_date='2024-05-31',hs='854143', start=0)))