# WMB_NEW

### 1.说明

1. wmb爬虫 - 重构
2. 记录各种统计信息（一部分为统计trade表，一部分由源网站提供，但是并不准确，只能作为参考）
3. 通过各种记录信息创建差异化的任务（比如按年，按页面doc_count数等），直至爬取完所有数据

#### 导出当前项目所需的包到requirements.txt

```shell script
pip3 install pipreqs
pipreqs . --encoding=utf8 --force
```

### docker compose(推荐)

#### 构建

```shell script
sh up.sh  #  推荐

# 构建并在后台启动容器
docker compose up -d
# 强制重新构建镜像并在后台启动容器
docker compose up -d --build
# 删除当前配置的所有镜像后执行上面的操作
docker compose down --rmi all && docker compose up -d --build
# 或者
docker compose up -d --build  && docker image prune -f
docker compose up -d --build container_name && docker image prune -f
```

#### 运维

```shell script
# 相应操作不带服务名即默认所有服务
docker-compose logs -f api
docker-compose logs --tail=100 -f monitor
docker-compose stop
docker-compose restart
```

### docker(弃用)

#### 构建

```shell script
docker build -t image_name:v1 .
docker build -t image_name:v1 -f Dockerfile_1 .
```

#### 启动

```shell script
docker run -d --name containor_name --network host -v --log-opt max-size=10m --log-opt max-file=3 ./logs:/app/logs image_name:v1
docker run -d --name containor_name --network host -v --log-opt max-size=10m --log-opt max-file=3 ./logs:/app/logs -e TASK_NAME=menu image_name:v1
```

#### 运维

##### 脚本运行

###### 添加临时环境变量

```shell script
# linux/macOS
export PYTHONPATH=$(pwd):$PYTHONPATH
# windows
set PYTHONPATH=%cd%;%PYTHONPATH%
# 运行
python bin/load_record.py
python test/test_api_trade.py
```

##### 查看容器实例信息

```shell script
docker ps -a | grep containor_name
```

##### 查看实时日志（或最后100条）

```shell script
docker logs $(docker ps -a | grep containor_name | awk '{print $1}') -n 100 -f
docker logs $(docker ps -a | grep containor_name | awk '{print $1}') 2>&1 | grep -E "WARNING|ERROR"
docker logs $(docker ps -a | grep containor_name | awk '{print $1}') 2>&1 | grep -E "WARNING|ERROR" > ./logs/error.log
```

##### 将当前目录copy到项目工作目录

```shell script
docker cp ./ $(docker ps -a | grep containor_name | awk '{print $1}'):/app/
```

##### 重启容器实例

```shell script
docker ps -a | grep containor_name | awk '{print $1}' | xargs docker restart
```

##### 交互方式进入容易内部（需要正在运行的容器）

```shell script
docker exec -it $(docker ps | grep containor_name | awk '{print $1}') /bin/bash
```

##### 删除容器实例

```shell script
docker ps -a | grep containor_name | awk '{print $1}' | xargs docker rm -f
```

#### 测试api

```shell script
curl -X 'POST' \
  'http://127.0.0.1:55001/test' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
      "key1": "value1",
      "key2": "value2",
    }'
```