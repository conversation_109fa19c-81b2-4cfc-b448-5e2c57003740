"""
根据公司去补充海关数据
"""
import asyncio

from wmb_trade import *
from spider import SpiderWmbTrade, SpiderWmbCompany
from concurrent.futures import wait
from public_utils_configs.util.thread_util import MyBoundThreadPoolExecutor
from public_utils_configs.util.mysql_pool import get_mysql_obj_by_conf


max_workers_number = 15  # 最大线程数
max_queue_number = 50  # 一次从数据库拿company_id的个数

executor = MyBoundThreadPoolExecutor(max_workers=max_workers_number, work_queue=max_queue_number)


class SupplementDataCompany:

    def __init__(self, **kwargs):
        self.spider_wmb_company = SpiderWmbCompany()
        self.scheduler_trade = SpiderWmbTrade()
        self.company_id = kwargs.get('company_id', 0)  # 数据源的公司id
        self.company_type = kwargs.get('company_type', 0)  # 进口数据（0是进口,1是出口）
        self.country = kwargs.get('country', '')  # 国家
        self.last_trade_date = kwargs.get('last_trade_date', '')  # 上次交易日期
        self.hs = kwargs.get('hs', '')  # hs_code
        self.start =  kwargs.get('start', 0)  # 开始位
        self.start_time = kwargs.get('start_time', '2001-01-01')   # 开始时间
        self.max_async_deal_number = 15  # 最大协程数

    async def get_company_all_hscode(self):
        data = await self.spider_wmb_company.downloader.search_hs_code(self.company_id, self.company_type)
        if isinstance(data,int):
            return []
        parsed_data = await self.spider_wmb_company.parser.parse_hs_code(data)
        search_item_list = []
        if parsed_data is None or len(parsed_data) == 0:
            return []
        else:
            for d in parsed_data:
                if not d.get('hs', ''):
                    continue
                search_obj = ModeTradeSearch(company_id=self.company_id,
                                             company_type=self.company_type,
                                             last_trade_date=d.get('last_trade_date', ''),
                                             start=0,
                                             hs=d.get('hs', ''),
                                             country=self.country,
                                             start_time=self.start_time
                                             )
                search_item_list.append(search_obj)
        return search_item_list

    async def consumer_main(self, parse_hscode_list):
        start_point = 0
        for point in range(start_point, len(parse_hscode_list), self.max_async_deal_number):
            task_list = parse_hscode_list[point: point + self.max_async_deal_number]
            consumers = [asyncio.create_task(self.scheduler_trade.worker(task)) for task in task_list]
            await asyncio.wait(consumers)

    async def get_company_id_by_hs_code(self,start_index=0):
        item = ModeSearchTask(start=start_index,key=self.hs,hs=self.hs,company_type=self.company_type,country=self.country,date_start='*',date_end='*',search_type='3')
        reset = 3
        while reset:
            reset -= 1
            company_data_obj = await self.spider_wmb_company.downloader.search_task(item)
            if not company_data_obj:
                continue
            total, company_data_list = await self.spider_wmb_company.parser.parse_task(company_data_obj)
            return  total, company_data_list
        return -1, []

    async def get_all_hscode_data_main(self):
        parse_hscode_list = await self.get_company_all_hscode()
        await self.consumer_main(parse_hscode_list)


def hscode_thread_deal(**kwargs):
    company_id = kwargs.get('company_id', 0)  # 数据源的公司id
    company_type = kwargs.get('company_type', 0)  # 进口数据（0是进口,1是出口）
    # country = kwargs.get('country', '')  # 国家
    # last_trade_date = kwargs.get('last_trade_date', 0)  # 上次交易日期
    # hs = kwargs.get('hs', 0)  # hs_code
    s = SupplementDataCompany(company_id=company_id, company_type=company_type)
    asyncio.run(s.get_all_hscode_data_main())

def company_thread_deal(**kwargs):
    company_id = kwargs.get('company_id', 0)  # 数据源的公司id
    company_type = kwargs.get('company_type', 0)  # 进口数据（0是进口,1是出口）
    s = SupplementDataCompany(company_id=company_id, company_type=company_type)
    model = ModeTradeSearch(company_id=s.company_id,company_type=s.company_type,country=s.country,start_time=s.start_time,last_trade_date=s.last_trade_date,start=s.start,hs=s.hs)
    asyncio.run(s.scheduler_trade.worker(model))
    data = asyncio.run(s.scheduler_trade.downloader.search_trade(model))
    # 解析贸易数据
    res = asyncio.run(s.scheduler_trade.parser.parse_trade(data,model))
    total, year_list, trade_list = res
    asyncio.run(s.scheduler_trade.db.save_trade(trade_list))

    # 补录公司的
    company_list = [(trade_obj.get('buyer_id'),0) for trade_obj in trade_list if trade_obj.get('company_id')] + [(trade_obj.get('seller_id'),1) for trade_obj in trade_list if trade_obj.get('company_id')]
    asyncio.run(s.spider_wmb_company.worker((company_id, company_type)))
    for obj in company_list:
        asyncio.run(s.spider_wmb_company.worker((obj[0],obj[1])))


def deal_hscode_customs(hs_code):
    code_list = [0, 1]
    for code in code_list:
        init_thread_buyer_list = []
        s = SupplementDataCompany(hs=hs_code, country='*', company_type=code)
        total, task_list = asyncio.run(s.get_company_id_by_hs_code())
        for company_obj in task_list:
            if not company_obj.get('company_id'):
                continue
            company_data = {
                'company_id': company_obj.get('company_id'),
                'company_type': code
            }
            init_thread_buyer_list.append(executor.submit(hscode_thread_deal, **company_data))
        wait(init_thread_buyer_list)
        page_number = int(total // 30)
        for x in range(1, page_number):
            thread_buyer_list = []
            total, task_list = asyncio.run(s.get_company_id_by_hs_code(start_index=x * 30))
            for company_obj in task_list:
                if not company_obj.get('company_id'):
                    continue
                company_data = {
                    'company_id': company_obj.get('company_id'),
                    'company_type': code
                }
                thread_buyer_list.append(executor.submit(hscode_thread_deal, **company_data))
            wait(thread_buyer_list)



def deal_main():
    mysql_dict = {
        'host': '********',
        'port': 3306,
        'user': 'data_server',
        'passwd': '7VSZ~a5qQ@mjsE',
        'db': 'src_wmb',
        'charset': 'utf8mb4'
    }
    mysql_obj = get_mysql_obj_by_conf(MySqlDeveProConf=mysql_dict)
    start_point = 0
    max_id_sql = "select max(id) as max_id from src_wmb.company"
    max_id_data = mysql_obj.select_data(max_id_sql)
    if max_id_data and max_id_data[0].get('max_id'):
        max_id_data = max_id_data[0].get('max_id')
        while start_point <= max_id_data:
            sql = f"select id,company_id from src_wmb.company where id > {start_point} order by id limit {max_queue_number}"
            data_list = mysql_obj.select_data(sql)
            if data_list:
                end_point = data_list[-1].get('id', 0)
                task_list = [data['company_id'] for data in data_list]
                thread_buyer_list = []
                thread_seller_list = []
                for company_id in task_list:
                    buyer_data = {
                        'company_id': company_id,
                        'company_type': 0
                    }
                    thread_buyer_list.append(executor.submit(hscode_thread_deal, **buyer_data))
                wait(thread_buyer_list)
                for company_id in task_list:
                    seller_data = {
                        'company_id': company_id,
                        'company_type': 1
                    }
                    thread_seller_list.append(executor.submit(hscode_thread_deal, **seller_data))
                wait(thread_seller_list)
                start_point = end_point + 1
            else:
                break





if __name__ == '__main__':
    # deal_hscode_customs(721934)

    # deal_main()
    task_list = [
        35396288
    ]
    task_list.reverse()
    for task_index in range(0, len(task_list), max_queue_number):
        now_tasks = task_list[task_index:task_index + max_queue_number]
        thread_buyer_list = []
        thread_seller_list = []
        for company_id in now_tasks:
            buyer_data = {
                'company_id': company_id,
                'company_type': 0
            }
            thread_buyer_list.append(executor.submit(company_thread_deal, **buyer_data))
        wait(thread_buyer_list)
        for company_id in now_tasks:
            seller_data = {
                'company_id': company_id,
                'company_type': 1
            }
            thread_seller_list.append(executor.submit(company_thread_deal, **seller_data))
        wait(thread_seller_list)


