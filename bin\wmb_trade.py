"""
1.新增贸易任务（有历史记录的和没有历史记录的）
    a.保存贸易记录
    b.保存按年统计贸易记录数
2.按年任务
    a.更新按年记录，根据数据差异创建任务
"""
import asyncio
import traceback
from spider import SpiderWmbTrade
from common.utils import statistics
from settings import WORKERS_TRADE, QUEUE_MAX_SIZE, logger
from spider.mode import *
from datetime import datetime

class SchedulerTrade:
    workers = WORKERS_TRADE
    max_size = QUEUE_MAX_SIZE

    def __init__(self):
        self.spider = SpiderWmbTrade(concurrency=self.workers)

    async def task_new(self):
        """每日新增 - 持续高并发"""
        workers = self.workers // 2
        queue = asyncio.Queue(maxsize=self.max_size)
        prod = asyncio.create_task(self.producer_new(queue, workers))
        consumers = [asyncio.create_task(self.consumer(queue)) for _ in range(workers)]
        await prod
        await queue.join()
        await asyncio.wait(consumers)

    async def task_history(self, year: int):
        """历史任务 - doc_count为0的部分 - 分批高并发"""
        semaphore = asyncio.Semaphore(self.workers)
        while True:
            # read
            data_src = await self.spider.read_task_history(year, limit=5000)
            # create task
            tasks = list()
            for data in data_src:
                year = data.pop('year')
                # data_count = data.pop('data_count')
                # doc_count = data.pop('doc_count')
                # diff = doc_count - data_count
                data.pop('data_count')
                data.pop('doc_count')
                item = ModeTradeSearch(**data)
                item.start_time = f"{year}-01-01"
                item.last_trade_date = min(f"{year}-12-31", f"{datetime.today():%Y-%m-%d}")
                tasks.append(asyncio.create_task(self.spider.worker_history(semaphore, item, year)))
            if len(tasks) > 0:
                await asyncio.wait(tasks)

    async def task_history_by_status(self, year: int, is_rg=False):
        """历史任务 - doc_count - data_count >= 100 and data_count <= 1000 - 分批高并发"""
        semaphore = asyncio.Semaphore(self.workers)
        aid = 0
        while True:
            # read
            data_src = await self.spider.read_task_history_by_status(year, aid, limit=5000, is_rg=is_rg)
            if len(data_src) == 0:
                if aid == 0:
                    break
                else:
                    aid = 0
                    continue
            # create task
            tasks = list()
            for data in data_src:
                aid = data.pop('aid')
                year = data.pop('year')
                # data_count = data.pop('data_count')
                # doc_count = data.pop('doc_count')
                data.pop('data_count')
                data.pop('doc_count')
                item = ModeTradeSearch(**data)
                item.start_time = f"{year}-01-01"
                item.last_trade_date = min(f"{year}-12-31", f"{datetime.today():%Y-%m-%d}")
                tasks.append(asyncio.create_task(self.spider.worker_history(semaphore, item, year, status=True)))
            if len(tasks) > 0:
                await asyncio.wait(tasks)

    async def producer_new(self, queue: asyncio.Queue, workers: int):
        """生产者 - 新任务"""
        aid = 0
        while True:
            queue_size = queue.qsize()
            logger.info({"queue_size": queue.qsize()})
            if queue_size < self.max_size:
                # 从mysql中读取数据
                data_src = await self.spider.read_task_new(aid=aid, limit=self.max_size)
                if len(data_src) == 0:
                    if aid > 0:
                        aid = 0
                        await asyncio.sleep(600)  # 没有新数据时休眠10分钟
                        continue
                    else:  # 结束生产
                        for _ in range(workers):
                            await queue.put(None)
                        break
                for data in data_src:
                    aid = data.pop('aid')
                    await queue.put(ModeTradeSearch(**data))
            else:
                await asyncio.sleep(30)  # 没有位置时休眠30s

    async def consumer(self, queue: asyncio.Queue):
        """通用队列消费者"""
        while True:
            try:
                item = await queue.get()
                if item is None:
                    break
                await self.spider.worker(item)
                # await self.spider.worker_history(item)
            except Exception as e:
                traceback.print_exc()
                await asyncio.sleep(1)
            finally:
                queue.task_done()


async def trade():
    p = SchedulerTrade()
    await p.task_new()  # 监控有更新数据的公司 - 通过国家筛选
    # await p.task_history(2023)
    # await p.task_history_by_status(2025)


@statistics
def ignition():
    asyncio.run(trade())


if __name__ == '__main__':
    ignition()
