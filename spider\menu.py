import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

import asyncio
import copy
from typing import List
from spider.mode import ModeSearchTask
from .downloader import DownloaderWMB
from parser import ParserWmb
from curd import MySQLWmb
from settings import logger, WORKERS_MENU
from common.utils import statistics


class SpiderWmbMenu:
    def __init__(self):
        self.downloader: DownloaderWMB = DownloaderWMB()
        self.parser: ParserWmb = ParserWmb()
        self.db: MySQLWmb = MySQLWmb()

    async def menu_country(self, incr=False):
        """更新国家最后的贸易记录日期 - 每日增量"""
        for i in range(2):
            # 0为供应商,1为买家(获取国家)
            res = await self.downloader.search_countries(i)
            # 处理数据 处理成
            # {国家英文名:{'en':国家英文名, 'cn': 国家中文名, 'flag': 0},.....,*:{"en": "*", "cn": "国际公司", "flag": 0}}
            parsed_data = await self.parser.parse_countries(res)
            # 过滤已抓取的
            country_dict = await self.db.read_country_list_by_update_time(company_type=i)
            parsed_data.update(country_dict)
            length = len(parsed_data)
            for k, (key, val) in enumerate(parsed_data.items(), 1):
                country_en = val['en']
                country_cn = val['cn']
                flag = val['flag']
                logger.info({"k": f"{k}/{length}", "country_en": val['en'], "country_cn": val['cn']})
                # 状态flag为1 and incr为True则continue
                if incr and flag == 1:
                    continue
                # 获取国家(供应商/买家)数据,并解析,然后解析数据,最后取第一条数据的last_trade_date当作该国家的最后更新日期,存入mysql
                item = ModeSearchTask(company_type=i, country=country_en, date_start="", date_end="", use_cookies=False)
                data = await self.downloader.search_task(item)
                if isinstance(data, int):
                    continue
                parsed_data = await self.parser.parse_task(data)
                # print(data)
                # print(parsed_data)
                if parsed_data is None:
                    continue
                total, items = parsed_data
                last_trade_date = items[0]["last_trade_date"]
                item = {
                    "country": country_en, "country_cn": country_cn, "company_type": i,
                    "total": total, "last_trade_date": last_trade_date
                }
                logger.info(item)
                await self.db.save_country_last_date([item])

    async def menu_companies(self):
        # 读取有更新的国家列表(有历史记录的情况)
        countries: List[dict] = await self.db.read_country_last_date(record=True)
        await self.companies(countries)
        # 读取有更新的国家列表(没有历史记录的情况)
        countries: List[dict] = await self.db.read_country_last_date(record=False)
        await self.companies(countries)

    async def companies(self, countries: List[dict]):
        """找到有新贸易数据的公司"""
        semaphore = asyncio.Semaphore(WORKERS_MENU)  # 控制并发数量
        length = len(countries)
        if length == 0:
            logger.info("没有新数据")
            return
        for k, info in enumerate(countries, 1):
            info_str = ','.join(map(str, info.values()))
            logger.info({"k": f"{k}/{length}", "page": 1, "info": info_str})
            # 访问第一页获取公司列表页数据
            info_search = copy.deepcopy(info)
            info_search.pop('country_cn')
            item_search_main = ModeSearchTask(**info_search)
            total = await self.search_task_by_page(semaphore, item_search_main)
            if total is None:
                continue
            logger.info({"total": total})
            if item_search_main.date_start != '':
                if total > 9990:  # 超出数量限制的记录
                    # 没有历史记录的任务，默认爬取最多前9990条
                    logger.warning(info)
                    await self.db.save_task_country_error(info)
                elif 0 < total <= 9990:
                    # 遍历其他页面
                    tasks = list()
                    for page in range(30, total, 30):
                        # page_progress = f"{page // 30 + 1}/{total // 30 + 1}"
                        # logger.info({"k": f"{k}/{length}", "page": page_progress, "info": list(info.values())})
                        info_cur = {"page": page // 30 + 1, "info": info_str}
                        item_search = ModeSearchTask(start=page, **info_search)
                        tasks.append(self.search_task_by_page(semaphore, item_search, info_cur))
                        # await self.search_task_by_page(semaphore, item_search)
                    c = 0
                    for core in asyncio.as_completed(tasks):
                        await core
                        c += 1
                        logger.info(f"{k}/{length} | {c}/{total // 30 + 1}")
            # 同步信息 - 国家信息表
            await self.db.update_record_to_task_country(item_search_main)

    async def search_task_by_page(self, semaphore, item: ModeSearchTask, info=None):
        """找到有新贸易数据的公司 - 按页码访问"""
        async with semaphore:
            if info is not None:
                logger.info(info)
            data = await self.downloader.search_task(item)
            if isinstance(data, int):
                return -1
            parsed_data = await self.parser.parse_task(data)
            if parsed_data is None:
                return
            total, items = parsed_data
            await self.db.save_task_new(items)
            return total

    async def detail_company(self):
        while True:
            data = await self.db.read_company_new()
            if data is None:
                break

    async def detail_trade(self):
        pass


async def test():
    p = SpiderWmbMenu()
    await p.detail_company()


@statistics
def ignition():
    asyncio.run(test())


if __name__ == '__main__':
    ignition()
