import asyncio
import json
from typing import List, Union, Optional
from common.utils import singleton
from common.database import PoolMysql, AsyncDBPoolMysql
from settings import *
from spider.mode import ModeSearchTask, ModeTradeSearch

pre = 50


@singleton
class MySQLWmb:
    def __init__(self, concurrency=1):
        # pool = PoolMysql(concurrency=int(concurrency * 1.5), **MYSQL_TARGET)
        pool = PoolMysql(concurrency=int(concurrency * 1.5), **MYSQL_COMPANY)
        self.db = AsyncDBPoolMysql(pool)
        self.table_task_country = "wmb_task_country"
        self.table_task_country_error = "wmb_task_country_error"
        self.table_task_new = "wmb_task_new"
        self.table_task_history = "wmb_task_history"
        self.table_company = "company"
        self.table_hscode = "hscode"
        self.table_product = "product"
        self.table_trade = "trade"  # 正式表

    async def read_country_list_by_update_time(self, company_type: Union[int, None] = None):
        # 获取表wmb_task_country所有company_type字段等于company_type值的数据(里面的数如果update_time>=当前时间(yy-mm-dd),则flag为1,反之为0)
        sql = (
            f"select country en, country_cn cn, if(update_time >= curdate(), 1, 0) flag from {self.table_task_country}")
        if isinstance(company_type, int) and 0 <= company_type <= 1:
            sql += f" where company_type={company_type}"
        data = await self.db.read(sql, return_dict=True)
        return dict() if data is None or len(data) == 0 else {r['en']: r for r in data}

    async def save_country_last_date(self, items: List[dict]):
        # 将数据写入wmb_task_country表
        # logger.info({"len": len(items), "pre": json.dumps(items[:2])[:pre]})
        logger.info(f"len:{len(items)},pre:{json.dumps(items[:2])[:pre]}")
        await self._save_data(self.table_task_country, items)

    async def read_country_last_date(self, record: bool = True):
        """读取国家有更新记录的 - 默认爬取最新一天的数据"""
        # record为True,则获取wmb_task_country表中的last_record_date!=last_trade_date并且last_trade_date不为空并且last_record_date不为空字符串的数据
        # record为False,则获取wmb_task_country表中的last_record_date!=last_trade_date并且last_trade_date不为空并且last_record_date为空字符串的数据
        # record为True就爬取已经有记录的并且已经更新的数据,反之则是爬取last_record_date为空字符串(初次记录)更新的数据
        symbol = '<>' if record else '='
        sql = (f"select country, country_cn, company_type, last_record_date, last_trade_date "
               f"from {self.table_task_country} "
               f"where last_record_date<>last_trade_date and last_trade_date<>'' and last_record_date{symbol}'' "
               f"order by last_trade_date desc")
        data_src = await self.db.read(sql)
        if data_src is None or len(data_src) == 0 or data_src[0] is None:
            return list()
        result = list()
        for country, country_cn, company_type, last_record_date, last_trade_date in data_src:
            item = dict()
            item['country'] = country
            item['country_cn'] = country_cn
            item['company_type'] = company_type
            item['date_start'] = last_record_date
            item['date_end'] = last_trade_date
            result.append(item)
        return result
    
    async def read_country(self) -> tuple:
        sql = f"select country_cn from {self.table_task_country}"
        data_src = await self.db.read(sql)
        return tuple() if data_src is None or len(data_src) == 0 or data_src[0][0] is None else data_src

    async def read_company_new(self, aid: int = 0, limit: int = 1000):
        """读取新增的公司"""
        # 读取aid列的值大于传入的aid的数据(只要aid, company_id, company_type列),要limit列
        logger.info({"aid": aid, "limit": limit})
        sql = (f"select aid, company_id, company_type from {self.table_task_new} "
               f"where aid>{aid} and flag=0 limit {limit}")
        logger.debug(sql)
        data_src = await self.db.read(sql)
        return list() if data_src is None or len(data_src) == 0 or data_src[0][1] is None else data_src

    async def update_last_trade_date(self,company_id,company_type,last_trade_date):
        sql = (f"update {self.table_task_new} set last_trade_date=GREATEST(last_trade_date, '{last_trade_date}') "
               f"where company_id={company_id} and company_type={company_type}")
        await self.db.execute(sql)

    async def update_company_status(self, company_id: int) -> bool:
        # 针对于wmb 已删除的companyId修改状态，置空flag status 以及last_trade_date
        # 返回sql执行状态
        sql = f"update {self.table_task_new} set flag = 1, status = 1, last_trade_date = '' where company_id={company_id}"
        return await self.db.execute(sql=sql)

    async def update_company_flag(self, company_id: int, company_type: int, last_trade_date: str):
        # 找到wmb_task_new表中company_id=传入的company_id的数据,把flag键修改成1
        # 更新指定公司（通过company_id和company_type确定）的last_trade_date字段，将其值设置为当前存储的last_trade_date和给定的last_trade_date中较大的那个日期
        logger.info(f"{company_id},{company_type},{last_trade_date}")
        sql = f"update {self.table_task_new} set flag=1 where company_id={company_id}"
        await self.db.execute(sql)
        await self.update_last_trade_date(company_id=company_id,company_type=company_type,last_trade_date=last_trade_date)

    async def read_task_trade_new(self, aid: int = 0, limit: int = 500) -> tuple:
        """读取明确有更新记录的公司，按最新日期排序，默认前5000条"""
        # 找到wmb_task_new表中aid大于传入aid的值,并且status等于0并且last_record_date不为空
        # 并且last_trade_date不为空并且last_trade_date>last_record_date,以last_trade_date降序,要limit条
        # (只展示aid, company_id, company_type, country, last_record_date start_time, last_trade_date)
        logger.info({"aid": aid, "limit": limit})
        # sql = (f"select aid, company_id, company_type, country, last_record_date start_time, last_trade_date "
        #        f"from {self.table_task_new} where aid>{aid} and `status`=0 and "
        #        f"last_record_date<>'' and last_trade_date<>'' and last_record_date<last_trade_date "
        #        f"order by last_trade_date desc limit {limit}")
        sql = (f"select aid, company_id, company_type, country, last_record_date start_time, last_trade_date "
               f"from {self.table_task_new} where aid>{aid} and `status`=0 and "
               f"last_record_date<>last_trade_date "
               f"order by last_trade_date desc limit {limit}")
        data_src = await self.db.read(sql, return_dict=True)
        return tuple() if data_src is None or len(data_src) == 0 else data_src

    async def update_record_to_task_country(self, item: ModeSearchTask):
        # 找到country=item.country and company_type=item.company_type的数据,并将last_record_date修改为item.date_end
        logger.info(f"{item.country},{item.company_type},{item.date_end}")
        sql = f"update {self.table_task_country} set last_record_date=%s where country=%s and company_type=%s;"
        await self.db.execute(sql, [item.date_end, item.country, item.company_type])

    async def read_task_trade_history(self, year: int, limit: int = 500) -> tuple:
        """读取历史任务"""

        logger.info({"year": year, "limit": limit})
        sql = (f"select company_id, country, company_type, `year`, data_count, doc_count "
               f"from {self.table_task_history} "
               f"where `year`='{year}' and doc_count=0 and `status`=0 limit {limit}")
        data_src = await self.db.read(sql, return_dict=True)
        return tuple() if data_src is None or len(data_src) == 0 else data_src

    async def read_task_trade_history_by_status(self, year: int, aid: int = 0, limit: int = 500) -> tuple:
        """读取历史任务"""
        logger.info({"year": year, "limit": limit})
        sql = (f"select aid, company_id, country, company_type, `year`, data_count, doc_count "
               f"from {self.table_task_history} "
               f"where `year`='{year}' and aid>{aid} and `status`=0 and "
               f"(doc_count>data_count or doc_count=0) limit {limit}")
        data_src = await self.db.read(sql, return_dict=True)
        return tuple() if data_src is None or len(data_src) == 0 else data_src

    async def read_task_trade_history_by_status_rg(self, year: int, aid: int = 0, limit: int = 500) -> tuple:
        """读取历史任务"""
        logger.info({"year": year, "limit": limit})
        sql = (f"select aid, company_id, country, company_type, `year`, data_count, doc_count "
               f"from {self.table_task_history} "
               f"where `year`='{year}' and aid>{aid} limit {limit}")
        data_src = await self.db.read(sql, return_dict=True)
        return tuple() if data_src is None or len(data_src) == 0 else data_src

    async def update_status_to_history(self, company_id: int, company_type: int, year: str, status: int = 1):
        logger.info(f"{company_id},{company_type},{year},{status}")
        sql = (f"update {self.table_task_history} set `status`={status} "
               f"where company_id={company_id} and company_type={company_type} and `year`={year!r}")
        await self.db.execute(sql)

    async def update_status_to_new(self, company_id: int, company_type: int, status: int = 1):
        logger.info(f"{company_id},{company_type}")
        sql = (f"update {self.table_task_new} set `status`={status} "
               f"where company_id={company_id} and company_type={company_type}")
        await self.db.execute(sql)

    async def update_data_count_by_year_to_history(self, company_id, company_type, year):
        """按年统计公司贸易数据，同步history表"""
        # count
        sql = (f"select count(*) from {self.table_trade} "
               f"where company_id={company_id} and year(`date`)={year}")
        data_src = await self.db.read(sql)
        if data_src is None or len(data_src) == 0:
            return
        data_count = data_src[0][0]
        # update
        logger.info(f"{company_id},{company_type},{year},{data_count}")
        sql = (f"update {self.table_task_history} set data_count=GREATEST(data_count, {data_count}) "
               f"where company_id={company_id} and company_type={company_type} and year='{year}'")
        await self.db.execute(sql)

    async def update_data_count_to_history(self, item: ModeTradeSearch):
        """按年统计公司贸易数据，同步history表"""
        company_id = item.company_id
        company_type = item.company_type
        country = item.country
        # count
        sql = (f"select year(`date`) `year`, count(*) from {self.table_trade} "
               f"where company_id={company_id} group by `year`")
        data_src = await self.db.read(sql)
        if data_src is None or len(data_src) == 0:
            return
        # update
        logger.info(f"{company_id},{company_type},{country},len:{len(data_src)}")
        items = [
            {"company_id": company_id, "company_type": company_type, "country": country, "year": y, "data_count": c}
            for y, c in data_src
        ]
        await self.db.save(self.table_task_history, items)

    async def save_task_country_error(self, item: dict):
        # logger.info({"len": len(item), "pre": json.dumps(item)[:pre]})
        logger.info(f"len:{len(item)},pre:{json.dumps(item)[:pre]}")
        await self._save_data(self.table_task_country_error, [item])

    async def save_task_new(self, items: List[dict]):
        # logger.info({"len": len(items), "pre": json.dumps(items[:2])[:pre]})
        logger.info(f"len:{len(items)},pre:{json.dumps(items[:2])[:pre]}")
        await self._save_data(self.table_task_new, items)

    async def save_task_history(self, items: List[dict], del_zero=True):
        # {"company_id": company_id,"country": country,"company_type": company_type,"year": year,"doc_count": doc_count}
        company_id = items[0]['company_id']
        company_type = items[0]['company_type']
        new_list = list()
        for item in items:
            doc_count = item['doc_count']
            if doc_count > 0:  # new
                new_list.append(item)
        # new
        # logger.info({"len": len(new_list), "pre": json.dumps(new_list[:2])[:pre]})
        logger.info(f"len:{len(new_list)},pre:{json.dumps(new_list[:2])[:pre]}")
        await self._save_data(self.table_task_history, items)
        if del_zero:
            # delete
            await self.del_task_history_zero_doc(company_id, company_type)

    async def del_task_history_zero_doc(self, company_id, company_type, year: Optional[str] = None):
        """从delete语句切换至update语句"""
        info = f"{company_id},{company_type}"
        # sql = (f"delete from {self.table_task_history} "
        #        f"where company_id={company_id} and company_type={company_type} and doc_count=0")
        sql = (f"update {self.table_task_history} set `status`=2 "
               f"where company_id={company_id} and company_type={company_type} and doc_count=0")
        if isinstance(year, str) and year.isdigit() and len(year) == 4:
            sql += f" and year<={year}"
            info += f",{year}"
        logger.info(info)
        await self.db.execute(sql)

    async def save_company(self, item: dict):
        # logger.info({"len": len(item), "pre": json.dumps(item)[:pre]})
        logger.info(f"len:{len(item)},pre:{json.dumps(item)[:pre]}")
        await self._save_data(self.table_company, [item])

    async def save_hs_code(self, items: List[dict]):
        # logger.info({"len": len(items), "pre": json.dumps(items[:2])[:pre]})
        logger.info(f"len:{len(items)},pre:{json.dumps(items[:2])[:pre]}")
        await self._save_data(self.table_hscode, items)

    async def save_product(self, items: List[dict]):
        # logger.info({"len": len(items), "pre": json.dumps(items[:2])[:pre]})
        logger.info(f"len:{len(items)},pre:{json.dumps(items[:2])[:pre]}")
        await self._save_data(self.table_product, items)

    async def save_trade(self, items: List[dict]):
        # logger.info({"len": len(items), "pre": json.dumps(items[:2])[:pre]})
        logger.info(f"len:{len(items)},pre:{json.dumps(items[:2])[:pre]}")
        await self._save_data(self.table_trade, items)

    async def update_last_trade_date_to_task_new(self, company_id, company_type, last_record_date, total):
        logger.info(f"{company_id},{company_type},{last_record_date}")
        sql = (f"update {self.table_task_new} set "
               f"last_record_date=GREATEST(last_record_date, '{last_record_date}'), "
               f"bill_count=GREATEST(bill_count, {total}), "
               f"`status`=1 "
               f"where company_id={company_id} and company_type={company_type}")
        await self.db.execute(sql)

    async def _save_data(self, table: str, items: List[dict]):
        fields = items[0].keys()
        insert_str = ', '.join(fields)
        update_str = ', '.join([f'{field}=values({field})' for field in fields])
        sql = (f"insert into {table}({insert_str}) value({', '.join(['%s'] * len(fields))}) "
               f"on duplicate key update {update_str}")
        insert_list = [list(item.values()) for item in items]
        logger.debug(sql)
        logger.debug(insert_list)
        await self.db.write(sql, insert_list)

    async def read_id_range(self, table: str, field: str = 'id', where: str = ''):
        sql = f"select min({field}), max({field}) from {table}"
        if len(where) > 0:
            sql += f" where {where}"
        data_raw = await self.db.read(sql)
        return 0, 0 if data_raw is None or len(data_raw) == 0 or data_raw[0] is None else data_raw[0]

    @staticmethod
    def id_iterator(min_id: int, max_id: int, batch_size: int):
        for i in range(min_id, max_id + 1, batch_size):
            start = i
            end = min(i + batch_size - 1, max_id)
            yield start, end

    async def test_connect(self):
        sql = "show tables;"
        result = await self.db.read(sql)
        print(result)

    async def close(self):
        await self.db.close()


async def test():
    p: MySQLWmb = MySQLWmb()
    # await p.test_connect()

    items = [
        {"company_id": 111, "company_type": 0, "country": "xxx", "last_record_date": "2023-01-01",
         "last_trade_date": "2023-01-01"},
        {"company_id": 222, "company_type": 1, "country": "yyy", "last_record_date": "2023-02-02",
         "last_trade_date": "2023-02-05"},
        {"company_id": 333, "company_type": 0, "country": "zzz", "last_record_date": "2023-03-03",
         "last_trade_date": "2023-06-01"},
    ]
    # await p.save_task_new(items)

    res = await p.test_connect()
    print(res)


if __name__ == '__main__':
    asyncio.run(test())
