import asyncio
import copy
from typing import List, <PERSON><PERSON>
from spider.mode import ModeSearchTask
from .downloader import DownloaderWMB
from parser import ParserWmb
from curd import MySQLWmb
from settings import logger, WORKERS_MENU



class SpiderCompanyToTrade:
    def __init__(self):
        self.downloader: DownloaderWMB = DownloaderWMB()
        self.parser: ParserWmb = ParserWmb()
        self.db: MySQLWmb = MySQLWmb()

    async def country_companies(self):
        """
        处理国家今年更新的数据
        """
        countries: Tuple = await self.db.read_country()
        for country in countries:
            pass

    async def run (self):
        pass