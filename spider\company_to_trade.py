import asyncio
import copy
import calendar
from datetime import datetime
from typing import List, <PERSON><PERSON>
from spider.mode import ModeSearchTask
from .downloader import DownloaderWMB
from parser import ParserWmb
from curd import MySQLWmb
from settings import logger, WORKERS_MENU


class SpiderCompanyToTrade:
    def __init__(self):
        self.downloader: DownloaderWMB = DownloaderWMB()
        self.parser: ParserWmb = ParserWmb()
        self.db: MySQLWmb = MySQLWmb()

    def generate_monthly_date_ranges(self) -> List[Tuple[str, str]]:
        """
        生成今年1月到7月的月初月尾日期范围
        返回: [('2025-01-01', '2025-01-31'), ('2025-02-01', '2025-02-28'), ...]
        """
        current_year = datetime.now().year  # 2025
        date_ranges = []

        # 今年1月到7月
        for month in range(1, 8):  # 1到7月
            # 获取该月的最后一天
            last_day = calendar.monthrange(current_year, month)[1]
            start_date = f"{current_year}-{month:02d}-01"
            end_date = f"{current_year}-{month:02d}-{last_day:02d}"
            date_ranges.append((start_date, end_date))

        return date_ranges

    async def get_country_mapping(self):
        """
        获取国家中英文名称映射
        返回: {country_cn: country_en} 的字典
        """
        # 获取国家列表，包含英文名和中文名
        country_dict = await self.db.read_country_list_by_update_time()

        # 构建中文名到英文名的映射
        cn_to_en_mapping = {}
        for country_en, country_info in country_dict.items():
            country_cn = country_info['cn']
            cn_to_en_mapping[country_cn] = country_en

        return cn_to_en_mapping

    async def manual_supplement_task(self):
        """
        手动补录任务：处理今年1月到7月的数据
        组合所有国家和时间范围进行数据补录
        """
        logger.info("开始手动补录任务：2025年1月到7月...")

        # 获取所有国家的中文名
        countries: Tuple = await self.db.read_country()
        if not countries:
            logger.warning("没有找到国家数据")
            return

        # 获取国家中英文名称映射
        country_mapping = await self.get_country_mapping()
        if not country_mapping:
            logger.warning("没有找到国家映射数据")
            return

        # 生成时间范围
        date_ranges = self.generate_monthly_date_ranges()
        logger.info(f"时间范围: {date_ranges}")

        # 控制并发数量
        semaphore = asyncio.Semaphore(WORKERS_MENU)

        # 遍历所有国家和时间范围的组合
        total_tasks = len(countries) * len(date_ranges) * 2  # 2个company_type
        current_task = 0

        for country_tuple in countries:
            country_cn = country_tuple[0]  # read_country返回的是(country_cn,)元组

            # 获取对应的英文名
            country_en = country_mapping.get(country_cn)
            if not country_en:
                logger.warning(f"找不到国家 {country_cn} 的英文名，跳过")
                continue

            for date_start, date_end in date_ranges:
                for company_type in [0, 1]:  # 0为买家，1为供应商
                    current_task += 1
                    logger.info({
                        "progress": f"{current_task}/{total_tasks}",
                        "country_cn": country_cn,
                        "country_en": country_en,
                        "date_range": f"{date_start} to {date_end}",
                        "company_type": company_type
                    })

                    # 创建搜索任务，使用英文名进行搜索
                    item = ModeSearchTask(
                        company_type=company_type,
                        country=country_en,  # 使用英文名
                        date_start=date_start,
                        date_end=date_end,
                        use_cookies=True
                    )

                    # 执行搜索任务
                    await self.process_country_month_task(semaphore, item, current_task, total_tasks)

    async def process_country_month_task(self, semaphore, item: ModeSearchTask, current_task: int, total_tasks: int):
        """
        处理单个国家月份任务，包含翻页逻辑
        """
        async with semaphore:
            try:
                # 获取第一页数据
                total = await self.search_task_by_page(item)
                if total is None or total <= 0:
                    logger.info(f"任务 {current_task}/{total_tasks} 无数据")
                    return

                logger.info({
                    "task": f"{current_task}/{total_tasks}",
                    "total_records": total,
                    "country": item.country,
                    "date_range": f"{item.date_start} to {item.date_end}",
                    "company_type": item.company_type
                })

                # 处理翻页
                if total > 9990:
                    # 超出数量限制的记录
                    logger.warning(f"任务 {current_task}/{total_tasks} 数据量超过9990，记录错误")
                    error_item = {
                        "country": item.country,
                        "company_type": item.company_type,
                        "date_start": item.date_start,
                        "date_end": item.date_end,
                        "total": total
                    }
                    await self.db.save_task_country_error(error_item)
                elif 30 < total <= 9990:
                    # 需要翻页处理
                    await self.process_pagination(item, total, current_task, total_tasks)

            except Exception as e:
                logger.error(f"任务 {current_task}/{total_tasks} 处理失败: {str(e)}")

    async def process_pagination(self, item: ModeSearchTask, total: int, current_task: int, total_tasks: int):
        """
        处理翻页逻辑
        """
        tasks = []
        page_count = (total // 30) + 1

        # 创建翻页任务
        for page in range(30, total, 30):
            page_num = (page // 30) + 1
            logger.info(f"任务 {current_task}/{total_tasks} 处理第 {page_num}/{page_count} 页")

            # 创建翻页搜索项
            item_page = ModeSearchTask(
                start=page,
                company_type=item.company_type,
                country=item.country,
                date_start=item.date_start,
                date_end=item.date_end,
                use_cookies=item.use_cookies
            )

            tasks.append(self.search_task_by_page(item_page))

        # 并发执行翻页任务
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for r in results if isinstance(r, int) and r > 0)
            logger.info(f"任务 {current_task}/{total_tasks} 翻页完成，成功页数: {success_count}/{len(tasks)}")

    async def search_task_by_page(self, item: ModeSearchTask):
        """
        搜索单页数据并保存
        参考 menu.py 中的 search_task_by_page 方法
        """
        try:
            data = await self.downloader.search_task(item)
            if isinstance(data, int):
                return -1

            parsed_data = await self.parser.parse_task(data)
            if parsed_data is None:
                return None

            total, items = parsed_data
            if items:
                await self.db.save_task_new(items)

            return total

        except Exception as e:
            logger.error(f"搜索页面失败: {str(e)}")
            return None

    async def country_companies(self):
        """
        处理国家今年更新的数据
        """
        countries: Tuple = await self.db.read_country()
        for country in countries:
            pass

    async def run(self):
        """
        运行手动补录任务
        """
        await self.manual_supplement_task()