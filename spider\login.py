import time
import requests
from curd import RedisWmb
from .proxy import ProxyStatic
from settings import logger, EXPIRE,TIMEINTERVAL


class Account:

    account = [
        # ('***********', 'Up@2022'),  # master1  fb8c070bfa2717ec
        # ('***********', '123456'),  # worker1.1  d00c71d80935b194  屏蔽
        # ('***********', 'abc12321'),  # worker1.2  10541cd2c7413c15  屏蔽
        # ('***********', '123456'),  # worker1.3  da73719cd534479d  屏蔽

        ('<EMAIL>', 'qwer273001'),  # master2  18b56a45fe63a3b3 有效期截止至2025-11-17 11:37:38
        ('***********', '123456'),  # worker2.1  d0c759b065bc65c2 普通账号
        ('<EMAIL>', '123456'),  # worker2.2  4def59c81502ef65 普通账号
        ('***********', 'aa123456'),  # worker2.3  53cdabde29b87343 普通账号
        ('***********','feijibei666!'),  # worker2.4  58fb542ff57ca366 有效期截止至2025-11-17 11:37:38

        ('***********', 'Susu18701!'),  # master3   9b5f68839ede90e8 有效期截止至2025-11-01 15:29:28
        ('***********', '159357!'),  # worker3.1   2bbedc55fba01913 有效期截止至2025-11-01 15:29:28
        ('***********', 'hwq15654*'),  # worker3.2  c7151c0c051674df 有效期截止至2025-11-01 15:29:28
        ('***********', 'SuSu19008!'),  # worker3.3   093b9689c384e129 有效期截止至2025-11-01 15:29:28
        ('***********', 'huangcx461356#'),  # worker3.4   8c8fde98aaa4e7f1 有效期截止至2025-11-01 15:29:28
    ]
    # 用于离线更新的账号
    offline_account = [
        # ('***********', '123456'),  # worker1.4  邦友900bfa07d9e03d9e
        # ('***********', 'Susu18701!'),  # master3  Susu
    ]

    def login_all(self, incr=False):
        # 用于所有账号的登录
        _proxy = ProxyStatic()  # 代理Ip对象
        _redis_wmb: RedisWmb = RedisWmb()  # 处理外贸帮的redis对象
        proxy = _proxy.get_proxy()  # 代理IP的对象
        proxy = None
        for username, password in self.account:
            key = f'wmb_cookie_{username}'  # 以账号为key
            logger.info(key)
            if incr:
                expire_time = _redis_wmb.get_cookies_expire(key)  # 获取存活时间
                if EXPIRE - expire_time < TIMEINTERVAL - 1:  # 如果规定时间(12小时) - 存活时间 < 2个小时,则暂时跳出,反之继续
                    continue
            cookies = self.login_single(username, password, proxy)  # 登录获取cookie
            if isinstance(cookies, str) and len(cookies) > 0:  # 如果cookie存在,就存在redis里面
                _redis_wmb.save_cookies(key, cookies)
        for username, password in self.offline_account:
            key = f'wmb_offline_cookie_{username}'
            logger.info(key)
            if incr:
                expire_time = _redis_wmb.get_cookies_expire(key)
                if EXPIRE - expire_time < TIMEINTERVAL - 1:
                    continue
            cookies = self.login_single(username, password, proxy)
            if isinstance(cookies, str) and len(cookies) > 0:
                _redis_wmb.save_cookies(key, cookies)
        _redis_wmb.close()

    @staticmethod
    def login_single(username, password, proxy=None):
        # 尝试3次进入sign页面登录,然后登录获取cookie,然后cookie不存在或者cookie但没有access_token,则continue重新再来一次;反之则继续走获取su_token,然后再把cookie返回去
        for _ in range(3):
            url = 'https://www.52wmb.com/async/sign'
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0',
                'Referer': 'https://www.52wmb.com/login?redirectUrl=https%3A%2F%2Fwww.52wmb.com%2F',
            }
            post_data = {"user_name": username, "password": password}
            try:
                resp = requests.post(url, headers=headers, data=post_data, proxies=proxy, timeout=60)
                logger.info(f'{resp.status_code} {url}')
                cookie1 = resp.cookies.get_dict()
                logger.info(f'[cookie1]: {cookie1}')
                if not cookie1 or 'access_token' not in cookie1:
                    logger.info(f'{username} {resp.text}')
                    time.sleep(5)
                    continue
                # 跳转到主页，获取su_token
                url = "https://www.52wmb.com/"
                headers = {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,"
                              "image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "Accept-Language": "zh-CN,zh;q=0.9",
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Pragma": "no-cache",
                    "Referer": "https://www.52wmb.com/login?redirectUrl=https%3A%2F%2Fwww.52wmb.com%2F",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "same-origin",
                    "Sec-Fetch-User": "?1",
                    "Upgrade-Insecure-Requests": "1",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                                  "Chrome/113.0.0.0 Safari/537.36",
                    "sec-ch-ua": "\"Google Chrome\";v=\"113\", \"Chromium\";v=\"113\", \"Not-A.Brand\";v=\"24\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\""
                }
                response = requests.get(url, headers=headers, cookies=cookie1, proxies=proxy)
                cookie2 = response.cookies.get_dict()
                logger.info(f'[cookie2]: {cookie2}')

                cookies = f'access_token={cookie1["access_token"]}; su_token={cookie2["su_token"]}'
                logger.info(f'[cookies]: {cookies}')
                return cookies
            except Exception as e:
                logger.error(f'登录失败:{e}')


if __name__ == '__main__':
    # res = Account.login_single("***********", "abc12321")
    # print(res)

    Account.log
