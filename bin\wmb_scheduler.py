#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: template_timed_task.py
Description: apscheduler调度模板
Author: Peng
Email: <EMAIL>
Date: 2023-07-07
"""

import time
import traceback
from spider import Account
from curd import RedisWmb
from common.monitor.fei_shu import *
from settings.setting import TIMEINTERVAL,WARNINGTIMEINTERVAL
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.cron import CronTrigger


def task1(incr=False):
    """登录任务、获取cookies"""
    for _ in range(3):
        try:
            Account().login_all(incr=incr)
        except Exception as e:
            print("login error:", str(e))
            traceback.print_exc()
        else:
            break


def ignition():
    last_time = 0
    last_warning_time = 0
    while True:
        now_time = int(time.time())
        if now_time - last_time >= TIMEINTERVAL:
            task1(incr=True)
            last_time = now_time
        _redis_wmb = RedisWmb()
        if now_time - last_warning_time >= WARNINGTIMEINTERVAL and len(_redis_wmb.get_cookies_key()) == 0: 
            FeiShuBot().send_text('wmb:当前redis账号池中账号条数为0 <at user_id="all">所有人</at>')
            last_warning_time = now_time



    # scheduler = BlockingScheduler()
    # task1(incr=True)
    # scheduler.add_job(task1, trigger=CronTrigger.from_crontab('0 */2 * * *'), max_instances=1)
    # scheduler.start()


if __name__ == '__main__':
    ignition()
