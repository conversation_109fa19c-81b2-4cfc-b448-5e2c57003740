from dataclasses import dataclass

from common.utils import today


@dataclass
class ModeSearchTask:
    start: int = 0
    key: str = ''
    hs: str = '*'
    company_type: int = 0
    country: str = "*"
    date_start: str = today()
    date_end: str = today()
    search_type:str = "2"
    use_cookies: bool = True


@dataclass
class ModeTradeSearch:
    company_id: int
    company_type: int
    country: str = '*'
    start_time: str = ""
    last_trade_date: str = ""
    start: int = 0
    hs: str = ''


if __name__ == '__main__':
    a = {"start": 1, "date_end": '2'}
    m = ModeSearchTask(**a)
    print(m)
