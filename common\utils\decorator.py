#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: decorator.py
Description: 常用装饰器
Author: Peng
Email: lansehulu<PERSON>@gmail.com
Date: 2023-10-13
"""

import time
from .str_processor import timestamp2str
from settings import *


def statistics(func):
    """程序统计信息 - 装饰器版 - 新"""

    def decorator(*args, **kwargs):
        start = time.time()
        total = func(*args, **kwargs)
        end = time.time()
        info = {
            "status": "complete",
            "start_time": timestamp2str(start),
            "end_time": timestamp2str(end),
            "duration_time": f"{round(end - start, 2)} sec",
        }
        if isinstance(total, int) and total > 0:
            info["quantity_completion"] = total
            res_time = end - start
            if not res_time:
                res_time = 0.001
            info["average_speed"] = f"{round(total / res_time, 2)} items/sec"
        logger.info(info)
        return total

    return decorator


def singleton(cls):
    """单例类装饰器 - 让一个正常的类变成单例模式"""
    instances = dict()

    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return get_instance
