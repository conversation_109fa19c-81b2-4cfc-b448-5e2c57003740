version: '3.8'  # docker-compose.yml文件格式的版本号
services:
  spider_wmb_scheduler: # 容器名(前面会自动加上项目目录的名字)
    container_name: spider_wmb_scheduler  # 容器名(指定完整的容器名，会忽略上面的容器名)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: spider_wmb:0.2  # 镜像名:标签
    command: python bin/wmb_scheduler.py  # 容器的启动命令
    volumes: # 数据卷
      - ./logs:/app/logs
    logging: # 日志
      options:
        max-size: "10m"
        max-file: "3"
    restart: on-failure  # 非0 状态码结束时重启

  spider_wmb_menu: # 容器名(前面会自动加上项目目录的名字)
    container_name: spider_wmb_menu  # 容器名(指定完整的容器名，会忽略上面的容器名)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: spider_wmb:0.2  # 镜像名:标签
    command: python bin/wmb_menu.py  # 容器的启动命令
    volumes: # 数据卷
      - ./logs:/app/logs
    logging: # 日志
      options:
        max-size: "10m"
        max-file: "3"
    restart: on-failure  # 非0 状态码结束时重启
    depends_on:
      - spider_wmb_scheduler

  #  spider_wmb_company:  # 容器名(前面会自动加上项目目录的名字)
  #    container_name: spider_wmb_company  # 容器名(指定完整的容器名，会忽略上面的容器名)
  #    build:
  #      context: .  # 指定当前目录为Dockerfile的目录
  #      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
  #    image: spider_wmb:0.2  # 镜像名:标签
  #    command: python bin/wmb_company.py  # 容器的启动命令
  #    volumes:  # 数据卷
  #      - ./logs:/app/logs
  #    logging:  # 日志
  #      options:
  #        max-size: "10m"
  #        max-file: "3"
  #    restart: on-failure  # 非0 状态码结束时重启
  #    depends_on:
  #      - spider_wmb_scheduler

  spider_wmb_trade: # 容器名(前面会自动加上项目目录的名字)
    container_name: spider_wmb_trade  # 容器名(指定完整的容器名，会忽略上面的容器名)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: spider_wmb:0.2  # 镜像名:标签
    command: python bin/wmb_trade.py  # 容器的启动命令
    volumes: # 数据卷
      - ./logs:/app/logs
    logging: # 日志
      options:
        max-size: "10m"
        max-file: "3"
    restart: on-failure  # 非0 状态码结束时重启
    depends_on:
      - spider_wmb_scheduler

#  spider_wmb_trade_his: # 容器名(前面会自动加上项目目录的名字)
#    container_name: spider_wmb_trade_his  # 容器名(指定完整的容器名，会忽略上面的容器名)
#    build:
#      context: .  # 指定当前目录为Dockerfile的目录
#      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
#    image: spider_wmb:0.2  # 镜像名:标签
#    command: python bin/wmb_trade_his.py  # 容器的启动命令
#    volumes: # 数据卷
#      - ./logs:/app/logs
#    logging: # 日志
#      options:
#        max-size: "10m"
#        max-file: "3"
#    restart: on-failure  # 非0 状态码结束时重启
#    depends_on:
#      - spider_wmb_scheduler
#
#  spider_wmb_trade_his1: # 容器名(前面会自动加上项目目录的名字)
#    container_name: spider_wmb_trade_his1 # 容器名(指定完整的容器名，会忽略上面的容器名)
#    build:
#      context: .  # 指定当前目录为Dockerfile的目录
#      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
#    image: spider_wmb:0.2  # 镜像名:标签
#    command: python bin/wmb_trade_his1.py  # 容器的启动命令
#    volumes: # 数据卷
#      - ./logs:/app/logs
#    logging: # 日志
#      options:
#        max-size: "10m"
#        max-file: "3"
#    restart: on-failure  # 非0 状态码结束时重启
#    depends_on:
#      - spider_wmb_scheduler

#  spider_wmb_trade_supplement: # 数据补录
#    container_name: spider_wmb_trade_supplement  # 容器名(指定完整的容器名，会忽略上面的容器名)
#    build:
#      context: .  # 指定当前目录为Dockerfile的目录
#      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
#    image: spider_wmb_supplement:0.1  # 镜像名:标签
#    command: python bin/wmb_supplement_data_company.py  # 容器的启动命令
#    volumes: # 数据卷
#      - ./logs:/app/logs
#    logging: # 日志
#      options:
#        max-size: "10m"
#        max-file: "3"


#  wmb_load_customs:  # 容器名(前面会自动加上项目目录的名字)
#    container_name: wmb_load_customs  # 容器名(指定完整的容器名，会忽略上面的容器名)
#    build:
#      context: .  # 指定当前目录为Dockerfile的目录
#      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
#    image: spider_wmb:0.2  # 镜像名:标签
#    command: python bin/load_customs.py  # 容器的启动命令
#    volumes:  # 数据卷
#      - ./logs:/app/logs
#    logging:  # 日志
#      options:
#        max-size: "10m"
#        max-file: "3"
#    restart: on-failure  # 非0 状态码结束时重启
