#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: counter.py
Description: 计数器
Author: Peng
Email: <EMAIL>
Date: 2023-10-13
"""

import threading
import asyncio
from .decorator import singleton


@singleton
class Counter:
    """计数器"""

    def __init__(self):
        self._value = 0

    @property
    def total(self):
        return self._value

    def add(self, num: int = 1):
        self._value += num

    def reset(self, num: int = 0):
        self._value = num


@singleton
class CounterThread:
    """计数器 - 多线程"""

    def __init__(self):
        self._value = 0
        self._lock = threading.Lock()

    @property
    def total(self):
        return self._value

    def add(self, num: int = 1):
        with self._lock:
            self._value += num

    def reset(self, num: int = 0):
        with self._lock:
            self._value = num


@singleton
class CounterAsync:
    """计数器 - 异步"""

    def __init__(self):
        self._total = 0
        self._lock = asyncio.Lock()

    @property
    def total(self):
        return self._total

    async def add(self, num: int = 1):
        async with self._lock:
            self._total += num

    async def reset(self, num: int = 0):
        async with self._lock:
            self._total = num
