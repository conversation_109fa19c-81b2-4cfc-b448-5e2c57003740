import random
import requests
from settings import logger, USE_PROXY


class ProxyTunnel:
    def get_proxy(self, back_dict: bool = True):
        proxy_list = [
            'http://789E1BA1:<EMAIL>:10491',  # 10并发，独享
            'http://80072ECB:<EMAIL>:11600',  # 20并发，与其他程序共用
        ]
        weights = [0.5, 0.5]
        proxy = random.choices(proxy_list, weights=weights, k=1)[0]
        logger.debug({"proxy": proxy})
        if back_dict:
            proxy = {
                "http": f"{proxy}",
                "https": f"{proxy}",
            }
        return proxy if USE_PROXY else None


class ProxyStatic:
    def get_proxy(self, back_dict: bool = True):
        return self._get_proxy_qg(back_dict=back_dict) if USE_PROXY else None

    def _get_proxy_qg(self, back_dict: bool = True):
        proxy_list = [
            # 'http://789E1BA1:<EMAIL>:10491',  # 10并发，独享
            'http://80072ECB:<EMAIL>:11600',  # 20并发，与其他程序共用
        ]
        if len(proxy_list) > 1:
            weights = [0.7, 0.3]
            proxy = random.choices(proxy_list, weights=weights, k=1)[0]
        elif len(proxy_list) == 1:
            proxy = random.choices(proxy_list, k=1)[0]
        else:
            raise KeyError("proxy list error")
        logger.debug({"proxy": proxy})
        if back_dict:
            proxy = {
                "http": f"{proxy}",
                "https": f"{proxy}",
            }
        return proxy
