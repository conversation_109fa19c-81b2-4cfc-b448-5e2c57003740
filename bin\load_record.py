import asyncio
from curd import MySQLWmb
from settings import logger
from common.utils import statistics


class LoadRecord:
    def __init__(self):
        self.db_wmb: MySQLWmb = MySQLWmb()
        # self.db_customs = DBCustoms()

    async def load_company_latest_date_cache(self):
        min_id, max_id = 150001, 16212083
        batch_size = 10000
        for i in range(min_id, max_id + 1, batch_size):
            start = i
            end = min(i + batch_size - 1, max_id)
            logger.info({"start": start, "end": end, "max_id": max_id})
            sql = (f"insert into wmb_task_new(company_id, company_type, country, last_record_date) "
                   f"select openid, company_type, country, last_record_date from t_trade_count_last_date_wmb "
                   f"where aid between {start} and {end} on duplicate key update "
                   f"company_id=values(company_id), company_type=values(company_type), country=values(country), "
                   f"last_record_date=values(last_record_date);")
            await self.db_wmb.db.execute(sql)

    async def load_company_all(self):
        """从公司表加载所有公司基本信息到任务表 - 全量"""
        table_src = "company"
        table_dst = "wmb_task_new"
        # read range
        min_id, max_id = await self.db_wmb.read_id_range(table_src, "id")

        batch_size = 100000
        for i in range(min_id, max_id + 1, batch_size):
            start = i
            end = i + batch_size - 1
            if end > max_id:
                end = max_id
            logger.info({"start": start, "end": end, "max_id": max_id})
            # read
            sql = (f"insert into {table_dst}(company_id, country, company_type) "
                   f"select company_id, country, case when source_url like '/buyer%' then 0 "
                   f"when source_url like '/supplier%' then 1 else -1 end company_type "
                   f"from {table_src} where id between {start} and {end} "
                   f"on duplicate key update company_id=values(company_id), country=values(country), "
                   f"company_type=values(company_type)")
            await self.db_wmb.db.execute(sql)

    async def load_company_latest_date(self):
        """更新公司最后交易记录 - 全量"""
        sql = (f"select t1.company_id, date_format(max(t2.`date`), '%Y-%m-%d') from "
               f"wmb_task_new t1 inner join trade t2 on t1.company_id=t2.company_id "
               f"group by t1.company_id")
        data_src = await self.db_wmb.db.read(sql)

        sql = f"update wmb_task_new set last_record_date=%s where company_id=%s"
        update_list = [[d, c] for c, d in data_src]
        await self.db_wmb.db.write(sql, update_list)
        # for k, data in enumerate(update_list, 1):
        #     print(k, data)

    async def load_history(self):
        table_1 = "wmb_task_new"
        table_2 = "trade"
        table_3 = "wmb_task_history"
        min_id, max_id = await self.db_wmb.read_id_range(table_1, 'aid')
        batch_size = 100000
        for start, end in self.db_wmb.id_iterator(min_id, max_id, batch_size):
            logger.info({"start": start, "end": end, "max_id": max_id})
            sql = (f"insert into {table_3}(company_id, `year`, data_count) "
                   f"select t1.company_id cid, year(t2.`date`) y, count(*) c from "
                   f"{table_1} t1 inner join {table_2} t2 on t1.company_id=t2.company_id "
                   f"where t1.aid between {start} and {end} group by cid, y "
                   f"on duplicate key update company_id=values(company_id), `year`=values(`year`), "
                   f"data_count=values(data_count)")
            await self.db_wmb.db.execute(sql)


@statistics
async def ignition():
    p = LoadRecord()
    # await p.load_history()
    await p.load_company_latest_date_cache()


if __name__ == '__main__':
    asyncio.run(ignition())
