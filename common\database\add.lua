-- add.lua
local key = KEYS[1]
local bit_size = tonumber(ARGV[1])
local hash_count = tonumber(ARGV[2])
local value = ARGV[3]

-- hash the value using mmh3
local function hash(_value, seed)
  local result = redis.sha1hex(_value .. seed)
  local h1 = tonumber(string.sub(result, 1, 8), 16)
  local h2 = tonumber(string.sub(result, 9, 16), 16)
  return (h1 + h2 * seed) % bit_size
end

-- set the bits at the hash indices
for seed = 1, hash_count do
  local index = hash(value, seed)
  redis.call("SETBIT", key, index, 1)
end

return true
