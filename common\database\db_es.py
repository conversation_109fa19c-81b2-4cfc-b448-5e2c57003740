#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: db_es.py
Description: es常用封装
Author: Peng
Email: <EMAIL>
Date: 2023-07-07
"""

import time
import requests
from typing import Union
from settings import *


class ElasticSearch:
    def __init__(self, url, user, passwd):
        self.auth = (user, passwd)
        self.es_url = url

    def search_max_date(self, source: int = 1, retry: int = 3, index_name="t_trade_new") -> Union[str, None]:
        """查询某个字段的最大值"""
        search_url = f"{self.es_url}/{index_name}/_search"
        body = {
            "size": 0,
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"source": source}},
                        # {"range": {"tradeId": {"gt": "200000000"}}}
                    ]
                }
            },
            "aggs": {
                "max_tradeDate": {
                    "max": {"field": "tradeDate"}  # 最后的日期（tradeDate为时间戳）
                }
            }
        }
        for _ in range(retry):
            try:
                response = requests.post(search_url, auth=self.auth, json=body, timeout=60)
                # print(response.text)
                data = response.json()
                # print(data)
                max_timestamp = data['aggregations']['max_tradeDate']['value']
                # print(max_timestamp)
                max_date = time.strftime('%Y-%m-%d', time.localtime(int(max_timestamp / 1000)))
                # print(max_date)
                return max_date
            except Exception as e:
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logger.warning(f"{exc_type.__name__}: {e}")
        logger.error({"msg": "最大重试次数", "source": source})

    def delete_by_query_batch(self, index_name: str, terms: dict):
        """批量删除"""
        url = f"{self.es_url}/{index_name}/_delete_by_query"
        # 例子
        # terms = {
        #     "tradeId": id_list  # id_list是一个唯一编号的列表
        # }
        body = {
            "query": {
                "terms": terms
            }
        }
        res = self.request_es(url, body)
        # print(res)
        # print(res.json())
        return res

    def insert_update_company_one(self, index_name: str, company_id: int, source: int):
        url = f"{self.es_url}/{index_name}/_doc/{company_id}"
        return self.request_es(url, body=source)

    def request_es(self, url: str, body, retry: int = 3):
        for _ in range(retry):
            try:
                response = requests.post(url, auth=self.auth, json=body, timeout=60)
                response.raise_for_status()
                return response
            except Exception as e:
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logger.warning(f"{exc_type.__name__}: {e}")
        logger.error({"msg": "最大重试次数", "body": body})
