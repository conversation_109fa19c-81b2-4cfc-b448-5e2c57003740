"""
入口:https://www.52wmb.com/customs-data/bangladesh?tags=sample
根据国家和hscode找对应的海关数据的公司,然后在对应公司找到对应的hscode,然后找到对应的数据再爬取下载
"""

import asyncio
from spider import SpiderWmbTrade, DownloaderWMB
from settings import logger
from spider.mode import ModeTradeSearch, ModeSearchTask
from parser.wmb import ParserWmb

download_wmb = DownloaderWMB()
parser_wmb = ParserWmb()
spider_wmb_trade = SpiderWmbTrade()


async def search_and_parse_hscode(params):
    """
    爬取与解析关于国家的hscode海关数据
    """
    response = await download_wmb.search_customs_detailed(params)
    if isinstance(response, dict) and isinstance(response.get('data'), dict) and isinstance(
            response['data'].get('detail'), dict):
        detail = response['data']['detail']
        clean_data = await parser_wmb.parse_trade_detailed(detail)
        return clean_data


async def search_and_parse_company(search_data):
    """
    爬取公司并解析,精确获取公司,进去获取hscode,所以start写死为0
    """

    hs_code = search_data['hs']
    search_company_item = ModeSearchTask(start=0,
                                         key=search_data['key'],
                                         company_type=search_data['company_type'],
                                         date_start=search_data['filter_date_start'],
                                         date_end=search_data['filter_date_end'],
                                         country=search_data['country'],
                                         )
    reset = 3
    parsed_data = []
    while reset:
        reset -= 1
        response = await download_wmb.search_task(search_company_item)
        if isinstance(response, dict):
            parsed_data = await parser_wmb.parse_task(response)
            break
    # print(parsed_data)
    if parsed_data and len(parsed_data) == 2:
        company_id_list = parsed_data[1]
        if len(company_id_list) > 0:
            company_data = company_id_list[0]
            search_company_item = ModeTradeSearch(company_id=company_data['company_id'],
                                                  company_type=company_data['company_type'],
                                                  country=company_data['country'],
                                                  last_trade_date=company_data['last_trade_date'],
                                                  hs=hs_code
                                                  )
            logger.info(
                f"根据(采购/供应)商id:{company_data['company_id']},hscode:{hs_code},国家:{company_data['country']},获取对应的海关数据")
            await spider_wmb_trade.worker(search_company_item)
            logger.info(
                f"(采购/供应)商id:{company_data['company_id']},hscode:{hs_code},国家:{company_data['country']},完成")


async def supplement_data_hscode(start=0, **kwargs):
    # 开始索引(1页20条,第一页为0,第n页为(n-1)*20)
    # 这些都是支持的参数,注释掉的是可选参数(start_date,end_date必传),没有注释的是必填
    country = kwargs.pop('country', '')
    start_date = kwargs.pop('start_date', '')  # 开始日期
    end_date = kwargs.pop('end_date', '')  # 进口数据
    # hs = kwargs.get('hs', '')  # 海关编码
    # des = kwargs.get('des', '')  # 产品描述
    # seller = kwargs.get('seller', '')  # 供应商
    # buyer = kwargs.get('buyer', '')  # 采购商
    # buyer_country = kwargs.get('buyer_country', '')  # 采购国
    # seller_country = kwargs.get('seller_country', '')  # 供应国
    # seller_port = kwargs.get('seller_port', '')  # 出口港
    # weight_min = kwargs.get('weight_min', '')  # 最小重量
    # weight_max = kwargs.get('weight_max', '')  # 最大重量
    # qty_min = kwargs.get('qty_min', '')  # 最小数量
    # qty_max = kwargs.get('qty_max', '')  # 最大数量
    # tag_id = kwargs.get('tag_id', '')  # 标签id(不必理会)
    result = await download_wmb.search_hsode_data(country=country,
                                                  start_date=start_date,
                                                  end_date=end_date,
                                                  start=start,
                                                  **kwargs)
    ie = kwargs.get('ie', 0)  # 进口数据（0是进口,1是出口）
    if isinstance(result, dict):
        total = result.get('hits', 0)  # 获取总数量
        content = result.get('content', '') if type(result.get('content', '')) != int else ''
        content = content.replace('\n', '').replace('\r', '')
        build_data_list = await parser_wmb.parse_hscode_current_data(content)
        params_list = [
            {'id': data['id'], 'ie': ie, 'trade_date': data['trade_date'], 'country': country} for data in
            build_data_list
        ]
        task_list = [
            search_and_parse_hscode(params) for params in params_list
        ]
        clean_data = await asyncio.gather(*task_list)
        new_clean_data = [data for data in clean_data if data]
        search_data_list = []
        for d in new_clean_data:
            search_obj = dict()
            search_obj['company_type'] = ie
            search_obj['country'] = country
            search_obj['hs'] = kwargs.get('hs', '')  # 海关编码
            search_obj['filter_date_start'] = start_date
            search_obj['filter_date_end'] = end_date
            if ie == 0 and d.get('buyer', ''):
                search_obj['key'] = d.get('buyer', '').lower() if d.get('buyer', '') else ''
                search_data_list.append(search_obj)
            elif ie == 1 and d.get('seller', ''):
                search_obj['key'] = d.get('seller', '').lower() if d.get('seller', '') else ''
                search_data_list.append(search_obj)
        task_list = [
            search_and_parse_company(search_obj) for search_obj in search_data_list
        ]
        await asyncio.gather(*task_list)
        logger.info(f'第{int(start / 20) + 1}页,国家:{country}完成')
        return total
    else:
        logger.warning(f'第{int(start / 20) + 1}页,国家:{country},海关数据找不到')
        return 0


async def main(**kwargs):
    all_number = await supplement_data_hscode(**kwargs)
    max_async = 10  # 最多10个异步一起执行
    if all_number > 20:
        step = 20
        start = 20
        start_task_point = 0  # 起始执行索引
        task_list = [
            supplement_data_hscode(start=_, **kwargs) for _ in range(start, all_number, step)
        ]
        for point in range(start_task_point, len(task_list), max_async):
            consumers = [
                asyncio.create_task(task) for task in task_list[point:point+max_async]
            ]
            await asyncio.wait(consumers)
    print('task finish')


if __name__ == '__main__':
    country = 'vietnam'
    start_date = '2022-07-31'
    end_date = '2024-07-31'
    hs = '87012190'
    asyncio.run(main(country=country, start_date=start_date,end_date=end_date,  seller_country='china',hs=hs))
