#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: setting.py
Description: 常用配置文件
Author: Peng
Email: <EMAIL>
Date: 2023-10-13
"""

import os
import sys
import yaml
import argparse
# import threading
from pathlib import Path
from loguru import logger

# 项目目录
DIR_CONF = Path(__file__).resolve().parent  # 项目根目录
DIR_PROJECT = Path(__file__).resolve().parent.parent  # 配置文件目录
# 命令行参数解析
parser = argparse.ArgumentParser()
parser.add_argument('-c', '--conf', default=None, help='conf file path')
# 参数字典
ARGS = parser.parse_args()

# 配置文件
file_yml_arg = ARGS.conf  # 命令行参数 - 优先级最高
file_yml_env = os.environ.get("conf", None)  # 环境变量参数 - 优先级次之
file_yml_default = ["config.yaml", "config.yml"]  # 默认配置文件路径
if file_yml_arg is not None:
    file_yml = file_yml_arg
elif file_yml_env is not None:
    file_yml = file_yml_env
else:
    for file in file_yml_default:
        file_path = Path(DIR_CONF, file)
        if file_path.exists():
            file_yml = file_path
            break
    else:
        raise FileNotFoundError("config.yaml not found")
# 读取配置文件
with open(file_yml, encoding='utf8') as f:
    CONFIG_DATA = yaml.safe_load(f)

# 日志
LEVEL_LIST = [  # 只会显示高于当前级别的日志信息
    'CRITICAL',  # 严重错误
    'ERROR',
    'WARNING',
    'INFO',
    'DEBUG'  # 调试
    'TRACE',  # 追踪
]
# 日志格式
log_format = (
    # 时间
    "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
    # 级别，info、warning、error...，<level>会根据级别来改变颜色</level>
    "<level>{level: <0}</level> | "
    # 进程号:线程号:名字:方法:行号
    "<cyan>"
    # "{extra[process]}:{extra[thread]}:"
    "{name}:{function}:{line}</cyan> | "
    # 消息
    "<level>{message}</level>"
)
logger.remove()

LOG_LEVEL_DEFAULT = "INFO"
LOG_LEVEL = CONFIG_DATA.get('log', dict()).get('level', LOG_LEVEL_DEFAULT).upper()
# 配置日志器
logger.add(sink=sys.stdout, format=log_format, level=LOG_LEVEL)
# 错误文件
DIR_LOGS = Path(DIR_PROJECT, 'logs')
DIR_LOGS.mkdir(exist_ok=True)
FILE_ERROR = Path(DIR_LOGS, f'error.log')
logger.add(sink=FILE_ERROR, format=log_format, level="WARNING", mode='w', encoding='utf8')

# def add_context(record):
#     # 添加额外的上下文信息
#     record["extra"]["process"] = os.getpid()  # 添加进程号
#     record["extra"]["thread"] = threading.get_ident()  # 添加线程号
#
#
# logger = logger.patch(add_context)

# 数据库
# redis
REDIS_URL = CONFIG_DATA.get('redis', dict()).get('url', '')

# mysql
MYSQL_DEFAULT = {
    'host': '127.0.0.1', 'port': 3306, 'user': 'root', 'passwd': 'root', 'db': 'test', 'charset': 'utf8mb4'
}
# MYSQL_TARGET = CONFIG_DATA.get('mysql', dict()).get('customs', MYSQL_DEFAULT)
# MYSQL_CUSTOMS = CONFIG_DATA.get('mysql', dict()).get('customs', MYSQL_DEFAULT)
MYSQL_COMPANY = CONFIG_DATA.get('mysql', dict()).get('company', MYSQL_DEFAULT)
# DATABASE_URL = f"mysql+aiomysql://" \
#                f"{MYSQL_TARGET.get('user', '')}:{MYSQL_TARGET.get('passwd', '').replace('@', '%40')}" \
#                f"@{MYSQL_TARGET.get('host', '')}/{MYSQL_TARGET.get('db', '')}"

USE_PROXY = CONFIG_DATA.get('use_proxy', True)
TIMEOUT = CONFIG_DATA.get('timeout', 30)
WORKERS_MENU = CONFIG_DATA.get('workers', dict()).get('menu', 1)
WORKERS_COMPANY = CONFIG_DATA.get('workers', dict()).get('company', 1)
WORKERS_TRADE = CONFIG_DATA.get('workers', dict()).get('trade', 1)
QUEUE_MAX_SIZE = CONFIG_DATA.get('max_size', 500)
EXPIRE = CONFIG_DATA.get('expire', 60 * 60 * 24)
TIMEINTERVAL = CONFIG_DATA.get('time_interval', 60 * 60 * 24)
WARNINGTIMEINTERVAL = CONFIG_DATA.get('warning_time_interval', 60 * 60 * 24)

if __name__ == '__main__':
    res = Path(file_yml).exists()
    print(res)
    # 绝对路径
    print(DIR_CONF.as_posix())
    # 相对路径
    print(DIR_CONF.relative_to(DIR_PROJECT).as_posix())

    print(MYSQL_COMPANY)
