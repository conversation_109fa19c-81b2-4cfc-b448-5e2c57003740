#!/usr/bin/env python3
# encoding: utf8

"""
测试手动补录任务脚本
"""

import sys
import os
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

import asyncio
from spider.company_to_trade import SpiderCompanyToTrade


async def test_date_ranges():
    """测试时间范围生成"""
    spider = SpiderCompanyToTrade()
    date_ranges = spider.generate_monthly_date_ranges()
    print("生成的时间范围:")
    for i, (start, end) in enumerate(date_ranges, 1):
        print(f"  {i}. {start} 到 {end}")
    return date_ranges


async def test_country_mapping():
    """测试国家映射"""
    spider = SpiderCompanyToTrade()
    try:
        country_mapping = await spider.get_country_mapping()
        print(f"\n国家映射数量: {len(country_mapping)}")
        print("前5个国家映射:")
        for i, (cn, en) in enumerate(list(country_mapping.items())[:5], 1):
            print(f"  {i}. {cn} -> {en}")
        return country_mapping
    except Exception as e:
        print(f"获取国家映射失败: {e}")
        return {}


async def test_countries():
    """测试获取国家列表"""
    spider = SpiderCompanyToTrade()
    try:
        countries = await spider.db.read_country()
        print(f"\n国家数量: {len(countries)}")
        print("前5个国家:")
        for i, country in enumerate(countries[:5], 1):
            print(f"  {i}. {country[0]}")
        return countries
    except Exception as e:
        print(f"获取国家列表失败: {e}")
        return tuple()


async def main():
    """主测试函数"""
    print("=" * 50)
    print("手动补录任务测试")
    print("=" * 50)
    
    # 测试时间范围生成
    date_ranges = await test_date_ranges()
    
    # 测试国家列表
    countries = await test_countries()
    
    # 测试国家映射
    country_mapping = await test_country_mapping()
    
    # 计算总任务数
    if countries and date_ranges:
        total_tasks = len(countries) * len(date_ranges) * 2
        print(f"\n总任务数: {total_tasks}")
        print(f"  国家数: {len(countries)}")
        print(f"  月份数: {len(date_ranges)}")
        print(f"  公司类型数: 2 (买家/供应商)")
    
    print("\n测试完成！")


if __name__ == '__main__':
    asyncio.run(main())
