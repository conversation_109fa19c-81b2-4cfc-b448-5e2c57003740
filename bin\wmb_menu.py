"""
1.通过api抓取国家列表，找到所有国家
2.遍历所有国家，获取最新数据日期，记录在mysql
3.对比mysql中日期有更新的国家，搜索公司（筛选国家和日期范围），记录结果在mysql
"""
import asyncio
from spider import SpiderWmbMenu, SpiderWmbCompany
from common.utils import statistics


async def menu():
    p = SpiderWmbMenu()
    await p.menu_country(incr=True)  # 监控国家列表
    await p.menu_companies()  # 监控有更新数据的公司 - 通过国家筛选

    p = SpiderWmbCompany()
    await p.task_one()


@statistics
def ignition():
    asyncio.run(menu())


if __name__ == '__main__':
    ignition()
