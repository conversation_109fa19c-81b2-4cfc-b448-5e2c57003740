"""
1.监控新增公司，爬取并记录公司相关信息和记录
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import asyncio
from spider import SpiderWmbCompany
from common.utils import statistics


async def detail():
    """更新较少，此处放弃运行，由menu顺带更新公司"""
    p = SpiderWmbCompany()
    await p.run()  # 监控有更新数据的公司 - 通过国家筛选
    # await p.task_one()


@statistics
def ignition():
    asyncio.run(detail())


if __name__ == '__main__':
    ignition()
